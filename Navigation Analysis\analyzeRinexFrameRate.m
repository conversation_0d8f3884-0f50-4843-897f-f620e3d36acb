function analyzeRinexFrameRate(filePath, expectedRate)

    % RINEX文件帧率分析示例脚本
    % 此脚本演示如何使用checkRinexFrameRate和plotRinexFrameRate函数
    % 分析RINEX观测文件(.25O)中的时间戳是否按照预期频率更新
    
    % 设置文件路径
    % filePath = 'F:\IE Projects\2025-7-3 MD01 test\20250703-DataSaved\1-ppp-ppk\sd_data_1_20250703_112336.25O';
    
    % 检查文件是否存在
    if ~exist(filePath, 'file')
        error('文件不存在: %s', filePath);
    end
    
    % 设置预期频率（Hz）
    % expectedRate = 1; % 1Hz
    
    % 分析RINEX文件帧率
    fprintf('正在分析RINEX文件: %s\n', filePath);
    fprintf('预期频率: %.1f Hz\n', expectedRate);
    [isValid, details] = checkRinexFrameRate(filePath, expectedRate);
    
    % 可视化结果
    plotRinexFrameRate(details);
    
    % % 保存结果
    % if ~isValid
    %     % 创建结果目录（如果不存在）
    %     resultDir = 'results';
    %     if ~exist(resultDir, 'dir')
    %         mkdir(resultDir);
    %     end
    % 
    %     % 提取文件名（不含路径和扩展名）
    %     [~, fileName, ~] = fileparts(filePath);
    % 
    %     % 保存图像
    %     saveas(gcf, fullfile(resultDir, [fileName, '_frame_rate_analysis.png']));
    % 
    %     % 保存详细信息到MAT文件
    %     save(fullfile(resultDir, [fileName, '_frame_rate_details.mat']), 'details');
    % 
    %     % 生成报告
    %     reportFile = fullfile(resultDir, [fileName, '_frame_rate_report.txt']);
    %     fid = fopen(reportFile, 'w');
    %     if fid ~= -1
    %         fprintf(fid, '=== RINEX文件帧率分析报告 ===\n');
    %         fprintf(fid, '文件: %s\n', filePath);
    %         fprintf(fid, '分析时间: %s\n', datestr(now));
    %         fprintf(fid, '预期频率: %.1f Hz (间隔 %.3f 秒)\n', expectedRate, details.expectedInterval);
    %         fprintf(fid, '实际频率统计:\n');
    %         fprintf(fid, '  - 最小间隔: %.3f 秒\n', details.statistics.min);
    %         fprintf(fid, '  - 最大间隔: %.3f 秒\n', details.statistics.max);
    %         fprintf(fid, '  - 平均间隔: %.3f 秒\n', details.statistics.mean);
    %         fprintf(fid, '  - 标准差: %.3f 秒\n', details.statistics.std);
    %         fprintf(fid, '异常点数量: %d (%.2f%%)\n', length(details.anomalies.indices), ...
    %             100 * length(details.anomalies.indices) / length(details.actualIntervals));
    % 
    %         if details.droppedFrames.count > 0
    %             fprintf(fid, '丢帧情况:\n');
    %             fprintf(fid, '  - 总丢帧数: %d 帧\n', details.droppedFrames.count);
    %             fprintf(fid, '  - 丢帧发生次数: %d 次\n', length(details.droppedFrames.locations));
    %             fprintf(fid, '  - 丢帧率: %.2f%%\n', ...
    %                 100 * details.droppedFrames.count / (length(details.actualIntervals) + details.droppedFrames.count));
    % 
    %             fprintf(fid, '丢帧详情:\n');
    %             for i = 1:length(details.droppedFrames.locations)
    %                 idx = details.droppedFrames.locations(i);
    %                 lostFrames = details.droppedFrames.framesPerDrop(i);
    %                 fprintf(fid, '  索引 %d: 丢失 %d 帧, 时间从 %.3f 到 %.3f, 间隔: %.3f 秒\n', ...
    %                     idx, lostFrames, details.timestamps(idx), details.timestamps(idx+1), details.actualIntervals(idx));
    %             end
    %         else
    %             fprintf(fid, '未检测到丢帧，异常可能由其他原因引起\n');
    %         end
    % 
    %         fclose(fid);
    %         fprintf('分析报告已保存到: %s\n', reportFile);
    %     end
    % end
    
    fprintf('分析完成\n');
end