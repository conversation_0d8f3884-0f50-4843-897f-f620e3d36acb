%% GNRMC数据提取与分析示例脚本
% 演示如何使用GNRMCAnalyzer类分析串口接收的GNRMC数据

%% 清理工作空间
clear; clc; close all;

%% 设置文件路径
% 请将此路径修改为您的实际数据文件路径
dataFile = 'F:\Analysis_Data\GPRMC\main-20250709-171417.txt';  % 替换为实际的数据文件名

% 检查文件是否存在
if ~exist(dataFile, 'file')
    fprintf('错误: 找不到数据文件 %s\n', dataFile);
    fprintf('请确保文件路径正确，或创建示例数据文件。\n');
    
    % 创建示例数据文件
    createSampleDataFile('sample_gnrmc_data.txt');
    dataFile = 'sample_gnrmc_data.txt';
    fprintf('已创建示例数据文件: %s\n', dataFile);
end

%% 创建GNRMC分析器并进行分析
try
    fprintf('开始分析GNRMC数据...\n');

    % 创建分析器实例（使用东八区时间）
    analyzer = GNRMCAnalyzer(dataFile, 8);

    % 获取提取的数据
    extractedData = analyzer.getExtractedData();
    
    % 获取时间差分析结果
    timeAnalysis = analyzer.getTimeAnalysis();
    
    % 显示基本统计信息
    if analyzer.extractedFrames > 0
        % 绘制分析图表
        analyzer.plotTimeDifferences();

        % 保存分析结果
        analyzer.saveResults();
        
        % 显示前几个数据帧的详细信息
        fprintf('\n=== 前5个数据帧详细信息 ===\n');
        numToShow = min(5, analyzer.extractedFrames);
        for i = 1:numToShow
            fprintf('帧 %d:\n', i);
            fprintf('  本地时间: %s\n', extractedData.localTimeStrings{i});
            fprintf('  UTC时间: %s\n', extractedData.utcTimeStrings{i});
            fprintf('  时间差: %.6f 秒\n', timeAnalysis.timeDifferences(i));
            fprintf('  GNRMC帧: %s\n', extractedData.rawFrames{i});
            fprintf('\n');
        end
    else
        fprintf('未找到有效的GNRMC数据帧\n');
    end
    
catch ME
    fprintf('分析过程中出现错误: %s\n', ME.message);
    fprintf('错误位置: %s (第%d行)\n', ME.stack(1).name, ME.stack(1).line);
end

%% 辅助函数：创建示例数据文件
function createSampleDataFile(filename)
    % createSampleDataFile - 创建包含GNRMC数据的示例文件
    
    fid = fopen(filename, 'w', 'n', 'UTF-8');
    if fid == -1
        error('无法创建示例文件');
    end
    
    % 写入示例数据
    fprintf(fid, '[16:13:22.841]Rx:\n');
    fprintf(fid, '$GNRMC,081325.00,A,2245.02702863,N,11347.63257941,E,0.008,151.7,090725,3.3,W,A,C*5A\n');
    fprintf(fid, '\n');
    
    fprintf(fid, '[16:13:23.842]Rx:\n');
    fprintf(fid, '$GNRMC,081326.00,A,2245.02702863,N,11347.63257941,E,0.008,151.7,090725,3.3,W,A,C*5B\n');
    fprintf(fid, '\n');
    
    fprintf(fid, '[16:13:24.843]Rx:\n');
    fprintf(fid, '$GNRMC,081327.00,A,2245.02702863,N,11347.63257941,E,0.008,151.7,090725,3.3,W,A,C*5C\n');
    fprintf(fid, '\n');
    
    fprintf(fid, '[16:13:25.844]Rx:\n');
    fprintf(fid, '$GNRMC,081328.00,A,2245.02702863,N,11347.63257941,E,0.008,151.7,090725,3.3,W,A,C*5D\n');
    fprintf(fid, '\n');
    
    fprintf(fid, '[16:13:26.845]Rx:\n');
    fprintf(fid, '$GNRMC,081329.00,A,2245.02702863,N,11347.63257941,E,0.008,151.7,090725,3.3,W,A,C*5E\n');
    fprintf(fid, '\n');
    
    fprintf(fid, '[16:13:27.846]Rx:\n');
    fprintf(fid, '$GNRMC,081330.00,A,2245.02702863,N,11347.63257941,E,0.008,151.7,090725,3.3,W,A,C*5F\n');
    fprintf(fid, '\n');
    
    fprintf(fid, '[16:13:28.847]Rx:\n');
    fprintf(fid, '$GNRMC,081331.00,A,2245.02702863,N,11347.63257941,E,0.008,151.7,090725,3.3,W,A,C*60\n');
    fprintf(fid, '\n');
    
    fprintf(fid, '[16:13:29.848]Rx:\n');
    fprintf(fid, '$GNRMC,081332.00,A,2245.02702863,N,11347.63257941,E,0.008,151.7,090725,3.3,W,A,C*61\n');
    fprintf(fid, '\n');
    
    fprintf(fid, '[16:13:30.849]Rx:\n');
    fprintf(fid, '$GNRMC,081333.00,A,2245.02702863,N,11347.63257941,E,0.008,151.7,090725,3.3,W,A,C*62\n');
    fprintf(fid, '\n');
    
    fprintf(fid, '[16:13:31.850]Rx:\n');
    fprintf(fid, '$GNRMC,081334.00,A,2245.02702863,N,11347.63257941,E,0.008,151.7,090725,3.3,W,A,C*63\n');
    fprintf(fid, '\n');
    
    fclose(fid);
    
    fprintf('示例数据文件已创建: %s\n', filename);
end
