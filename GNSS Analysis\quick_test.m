%% 快速测试GNRMC分析器的修复功能
clear; clc; close all;

fprintf('=== 快速测试GNRMC分析器 ===\n');

% 创建简单的测试数据
testFile = 'quick_test_data.txt';
fid = fopen(testFile, 'w', 'n', 'UTF-8');
fprintf(fid, '[08:13:20.100]Rx:\n');
fprintf(fid, '$GNRMC,001320.00,A,2245.02702863,N,11347.63257941,E,0.008,151.7,090725,3.3,W,A,C*5A\n');
fprintf(fid, '\n');
fprintf(fid, '[08:13:21.101]Rx:\n');
fprintf(fid, '$GNRMC,001321.00,A,2245.02702863,N,11347.63257941,E,0.008,151.7,090725,3.3,W,A,C*5B\n');
fprintf(fid, '\n');
fprintf(fid, '[08:13:22.102]Rx:\n');
fprintf(fid, '$GNRMC,001322.00,A,2245.02702863,N,11347.63257941,E,0.008,151.7,090725,3.3,W,A,C*5C\n');
fclose(fid);

try
    % 测试分析器创建
    fprintf('1. 测试分析器创建...\n');
    analyzer = GNRMCAnalyzer(testFile, 8);
    fprintf('   ✓ 分析器创建成功\n');
    
    % 测试数据提取
    fprintf('2. 测试数据提取...\n');
    fprintf('   提取的帧数: %d\n', analyzer.extractedFrames);
    
    % 测试时间分析
    fprintf('3. 测试时间分析...\n');
    analysis = analyzer.getTimeAnalysis();
    if ~isempty(analysis)
        fprintf('   ✓ 时间分析完成\n');
        fprintf('   平均时间差: %.6f 秒\n', analysis.meanTimeDiff);
    end
    
    % 测试可视化（不显示图形）
    fprintf('4. 测试可视化功能...\n');
    set(0, 'DefaultFigureVisible', 'off');
    analyzer.plotTimeDifferences();
    set(0, 'DefaultFigureVisible', 'on');
    fprintf('   ✓ 可视化功能正常\n');
    
    % 测试结果打印
    fprintf('5. 测试结果打印...\n');
    analyzer.printAnalysisResults();
    
    fprintf('\n=== 所有测试通过！ ===\n');
    
catch ME
    fprintf('❌ 测试失败: %s\n', ME.message);
    if ~isempty(ME.stack)
        fprintf('   错误位置: %s (第%d行)\n', ME.stack(1).name, ME.stack(1).line);
    end
end

% 清理测试文件
if exist(testFile, 'file')
    delete(testFile);
end
