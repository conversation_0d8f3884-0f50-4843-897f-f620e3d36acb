classdef BB00Parser < DatafileParser
    % BB00Parser - 用于解析BB00协议CSV文件并管理数据的类
    % 继承自DatafileParser抽象父类

    properties (Access = public)
        szImuName       = 'INS370-25J'
        szProgramName   = 'MD01'
        columnNames = { ...
            '帧计数','FPGA版本','WATCH版本','X齿轮','X左前轮速','X右前轮速','X左后轮速','X右后轮速','XCan信息计数', ...
            '陀螺-X','陀螺-Y','陀螺-Z','陀螺温度-X','陀螺温度-Y','陀螺温度-Z', ...
            '加计-X','加计-Y','加计-Z','加计温度','保留1','保留2','保留3', ...
            'GNSS周','GNSS毫秒','gnss秒','PPS延迟','卫星数','RTK状态','速度状态','真北航迹方向', ...
            '北向速度','东向速度','天向速度','位置状态','纬度方向','纬度','经度方向','经度','高度', ...
            '航向状态','基线长','横滚','俯仰','偏航','齿轮','Can信息计数','左前轮速','右前轮速','左后轮速','右后轮速', ...
            '时间精度','垂直精度','水平精度','北向精度','东向精度','截止至高角','纬度标准差','经度标准差','高度标准差', ...
            '航向标准差','俯仰标准差','解状态','位置类型','Checksum','帧计数','纬度','经度','高度', ...
            'A-东向速度','A-北向速度','A-天向速度','A-俯仰','A-横滚','A-偏航', ...
            '校准陀螺x','校准陀螺y','校准陀螺z','校准加速度x','校准加速度y','校准加速度z', ...
            'FPGA中断计数','包计数','系统状态'};
    end

    methods
        function obj = BB00Parser(filename)
            % BB00Parser - 构造函数
            % 调用父类构造函数
            obj@DatafileParser(filename);
        end

    end

    methods (Access = protected)
        function parseCSV(obj)
            % parseCSV - 解析CSV文件并将数据存储到分层结构体中
            % 实现父类的抽象方法
            try
                % 使用父类的通用读取方法
                obj.readCSVFile();

                obj.dataStruct = struct();
                % IMU数据
                obj.dataStruct.IMU = struct(...
                    'Gyro', struct(...
                        'X', obj.rawData{:,10}, ...
                        'Y', obj.rawData{:,11}, ...
                        'Z', obj.rawData{:,12}), ...
                    'Accel', struct(...
                        'X', obj.rawData{:,16}, ...
                        'Y', obj.rawData{:,17}, ...
                        'Z', obj.rawData{:,18}) ...
                );
                % GNSS时间
                obj.dataStruct.Time = struct(...
                    'GNSSWeek', obj.rawData{:,23}, ...
                    'GNSSMilliseconds', obj.rawData{:,24}, ...
                    'GNSSSeconds', obj.rawData{:,25} ...
                );
                % 卫星与RTK
                obj.dataStruct.SatInfo = struct(...
                    'SatelliteCount', obj.rawData{:,27}, ...
                    'RTKStatus', obj.rawData{:,28} ...
                );
                % GNSSdata（GNSS卫导原始数据，30~44列，三维数组形式）
                obj.dataStruct.GNSSdata = struct(...
                    'Velocity', [obj.rawData{:,32}, obj.rawData{:,31}, obj.rawData{:,32}], ... % 东北天
                    'Position', [obj.rawData{:,38}, obj.rawData{:,36}, obj.rawData{:,39}], ... % 经度、纬度、高度
                    'Attitude', [obj.rawData{:,42}, obj.rawData{:,43}, obj.rawData{:,44}] ... % 横滚、俯仰、航向
                );   
                % 组合导航解算数据（66~74列）
                obj.dataStruct.Position = struct(...
                    'Longitude', obj.rawData{:,67}, ...
                    'Latitude', obj.rawData{:,66}, ...
                    'Altitude', obj.rawData{:,68});
                obj.dataStruct.Velocity = struct(...
                    'East', obj.rawData{:,69}, ...
                    'North', obj.rawData{:,70}, ...
                    'Up', obj.rawData{:,71});
                if isprop(obj, 'szImuName') && strcmp(obj.szImuName, 'INS370-25J')
                    obj.dataStruct.Attitude = struct(...
                        'Pitch', obj.rawData{:,72}, ...
                        'Roll', obj.rawData{:,73}, ...
                        'Yaw', obj.headingToYaw(obj.rawData{:,74})); % 输出的是航向角，需要统一为偏航角
                else
                    obj.dataStruct.Attitude = struct(...
                        'Pitch', obj.rawData{:,72}, ...
                        'Roll', obj.rawData{:,73}, ...
                        'Yaw', obj.rawData{:,74});
                end

                % 野值检测
                obj.detectOutliersInNavData();
                % 其他信息
                obj.dataStruct.Other = struct(...
                    'FrameCount', obj.rawData{:,1}, ...
                    'FPGA_Version', obj.rawData{:,2}, ...
                    'WATCH_Version', obj.rawData{:,3}, ...
                    'Checksum', obj.rawData{:,62}, ...
                    'SystemStatus', obj.rawData{:,79} ...
                );
                % 使用父类方法计算数据输出频率
                gnss_ms = obj.rawData{:,24}; % GNSS毫秒
                obj.calculateOutputRate(gnss_ms, 'ms');

                % 调用特殊GNSS时间修正
                obj.fixSpecialGnssTimeJumps();
                
                disp('BB00 CSV文件解析完成，数据已存储在分层的dataStruct属性中。');
            catch ME
                error('解析BB00 CSV文件时出错: %s\n请检查文件格式和列顺序是否与BB00协议一致。', ME.message);
            end
        end
        
        function navObj = toComNavResult(obj)
            % toComNavResult - 基于解析的数据生成一个ComNavResult对象
            % 输出:
            %   navObj - ComNavResult类实例
            % 使用父类的通用方法，并添加BB00特有的GNSSdata处理
            navObj = toComNavResult@DatafileParser(obj);

            % BB00特有的GNSSdata处理
            if isfield(obj.dataStruct, 'GNSSdata')
                first_valid = obj.findFirstValidFrame();
                gnssdata = obj.dataStruct.GNSSdata;

                if isfield(gnssdata, 'Velocity') && size(gnssdata.Velocity,1) >= first_valid
                    gnssdata.Velocity = gnssdata.Velocity(first_valid:end, :);
                end
                if isfield(gnssdata, 'Position') && size(gnssdata.Position,1) >= first_valid
                    gnssdata.Position = gnssdata.Position(first_valid:end, :);
                end
                if isfield(gnssdata, 'Attitude') && size(gnssdata.Attitude,1) >= first_valid
                    gnssdata.Attitude = gnssdata.Attitude(first_valid:end, :);
                end

                navObj = navObj.setGNSSdata(gnssdata);
            end
        end
    end

    methods (Access = private)
        function detectOutliersInNavData(obj)
            % detectOutliersInNavData - 检查组合导航解算数据中的野值，并补偿突变
            if ~isfield(obj.dataStruct, 'Position') || ~isfield(obj.dataStruct, 'Velocity') || ~isfield(obj.dataStruct, 'Attitude')
                warning('缺少组合导航解算数据，无法进行野值检测');
                return;
            end
            pos = obj.dataStruct.Position;
            vel = obj.dataStruct.Velocity;
            att = obj.dataStruct.Attitude;
            % 检查经纬高
            lon = pos.Longitude; lat = pos.Latitude; alt = pos.Altitude;
            if any(abs(lon) > 180) || any(abs(lat) > 90)
                warning('经纬度存在超出物理范围的野值');
            end
            if any(abs(alt) > 1e4)
                warning('高度存在异常野值(>10km)');
            end
            % 检查速度
            v = [vel.East, vel.North, vel.Up];
            if any(abs(v(:)) > 100)
                warning('速度存在异常野值(>100m/s)');
            end
            % 检查姿态
            pitch = att.Pitch; roll = att.Roll; yaw = att.Yaw;
            if any(abs(pitch) > 90) || any(abs(roll) > 180) || any(abs(yaw) > 360)
                warning('姿态角存在异常野值');
            end
            % 检查突变（差分大于阈值）并补偿
            [dLon, dLat, dAlt] = deal(abs(diff(lon)), abs(diff(lat)), abs(diff(alt)));
            jumpIdxLon = find(dLon > 0.01) + 1;
            jumpIdxLat = find(dLat > 0.01) + 1;
            jumpIdxAlt = find(dAlt > 10) + 1;
            jumpIdx = unique([jumpIdxLon; jumpIdxLat; jumpIdxAlt]);
            if ~isempty(jumpIdx)
                warning('经纬高存在突变野值，共检测到%d个突变点', numel(jumpIdx));
                lon = BB00Parser.compensateJumps(lon, jumpIdx);
                lat = BB00Parser.compensateJumps(lat, jumpIdx);
                alt = BB00Parser.compensateJumps(alt, jumpIdx);
                obj.dataStruct.Position.Longitude = lon;
                obj.dataStruct.Position.Latitude = lat;
                obj.dataStruct.Position.Altitude = alt;
            end
            dV = abs(diff(v));
            jumpIdxV = find(any(dV > 10,2)) + 1;
            if ~isempty(jumpIdxV)
                warning('速度存在突变野值，共检测到%d个突变点', numel(jumpIdxV));
                for k = 1:3
                    v(:,k) = BB00Parser.compensateJumps(v(:,k), jumpIdxV);
                end
                obj.dataStruct.Velocity.East = v(:,1);
                obj.dataStruct.Velocity.North = v(:,2);
                obj.dataStruct.Velocity.Up = v(:,3);
            end
            dPitch = abs(diff(pitch)); dRoll = abs(diff(roll)); dYaw = abs(diff(yaw));
            jumpIdxPitch = find(dPitch > 10) + 1;
            jumpIdxRoll = find(dRoll > 10) + 1;
            jumpIdxYaw = find(dYaw > 20) + 1;
            if ~isempty(jumpIdxPitch)
                warning('俯仰角存在突变野值，共检测到%d个突变点', numel(jumpIdxPitch));
                pitch = BB00Parser.compensateJumps(pitch, jumpIdxPitch);
                obj.dataStruct.Attitude.Pitch = pitch;
            end
            if ~isempty(jumpIdxRoll)
                warning('横滚角存在突变野值，共检测到%d个突变点', numel(jumpIdxRoll));
                roll = BB00Parser.compensateJumps(roll, jumpIdxRoll);
                obj.dataStruct.Attitude.Roll = roll;
            end
            if ~isempty(jumpIdxYaw)
                warning('偏航角存在突变野值，共检测到%d个突变点', numel(jumpIdxYaw));
                yaw = BB00Parser.compensateJumps(yaw, jumpIdxYaw);
                obj.dataStruct.Attitude.Yaw = yaw;
            end
            % 检查GNSSdata.Position的野值和突变
            if isfield(obj.dataStruct, 'GNSSdata') && isfield(obj.dataStruct.GNSSdata, 'Position')
                gnssPos = obj.dataStruct.GNSSdata.Position;
                lon_gnss = gnssPos(:,1); lat_gnss = gnssPos(:,2); alt_gnss = gnssPos(:,3);
                % 物理范围野值
                if any(abs(lon_gnss) > 180) || any(abs(lat_gnss) > 90)
                    warning('GNSSdata.Position经纬度存在超出物理范围的野值');
                end
                if any(abs(alt_gnss) > 1e4)
                    warning('GNSSdata.Position高度存在异常野值(>10km)');
                end
                % 突变检测
                dLon_gnss = abs(diff(lon_gnss)); dLat_gnss = abs(diff(lat_gnss)); dAlt_gnss = abs(diff(alt_gnss));
                jumpIdxLon_gnss = find(dLon_gnss > 0.01) + 1;
                jumpIdxLat_gnss = find(dLat_gnss > 0.01) + 1;
                jumpIdxAlt_gnss = find(dAlt_gnss > 10) + 1;
                jumpIdx_gnss = unique([jumpIdxLon_gnss; jumpIdxLat_gnss; jumpIdxAlt_gnss]);
                if ~isempty(jumpIdx_gnss)
                    warning('GNSSdata.Position存在突变野值，共检测到%d个突变点', numel(jumpIdx_gnss));
                    lon_gnss = BB00Parser.compensateJumps(lon_gnss, jumpIdx_gnss);
                    lat_gnss = BB00Parser.compensateJumps(lat_gnss, jumpIdx_gnss);
                    alt_gnss = BB00Parser.compensateJumps(alt_gnss, jumpIdx_gnss);
                    obj.dataStruct.GNSSdata.Position(:,1) = lon_gnss;
                    obj.dataStruct.GNSSdata.Position(:,2) = lat_gnss;
                    obj.dataStruct.GNSSdata.Position(:,3) = alt_gnss;
                end
            end
            % 检查GNSSdata.Attitude的野值和突变
            if isfield(obj.dataStruct, 'GNSSdata') && isfield(obj.dataStruct.GNSSdata, 'Attitude')
                gnssAtt = obj.dataStruct.GNSSdata.Attitude;
                if size(gnssAtt,2) == 3
                    pitch_gnss = gnssAtt(:,1); roll_gnss = gnssAtt(:,2); yaw_gnss = gnssAtt(:,3);
                    % 物理范围野值
                    if any(abs(pitch_gnss) > 90) || any(abs(roll_gnss) > 180) || any(abs(yaw_gnss) > 360)
                        warning('GNSSdata.Attitude存在超出物理范围的野值');
                    end
                    % 突变检测
                    dPitch_gnss = abs(diff(pitch_gnss)); dRoll_gnss = abs(diff(roll_gnss)); dYaw_gnss = abs(diff(yaw_gnss));
                    jumpIdxPitch_gnss = find(dPitch_gnss > 10) + 1;
                    jumpIdxRoll_gnss = find(dRoll_gnss > 10) + 1;
                    jumpIdxYaw_gnss = find(dYaw_gnss > 20) + 1;
                    if ~isempty(jumpIdxPitch_gnss)
                        warning('GNSSdata.Attitude俯仰角存在突变野值，共检测到%d个突变点', numel(jumpIdxPitch_gnss));
                        pitch_gnss = BB00Parser.compensateJumps(pitch_gnss, jumpIdxPitch_gnss);
                        obj.dataStruct.GNSSdata.Attitude(:,1) = pitch_gnss;
                    end
                    if ~isempty(jumpIdxRoll_gnss)
                        warning('GNSSdata.Attitude横滚角存在突变野值，共检测到%d个突变点', numel(jumpIdxRoll_gnss));
                        roll_gnss = BB00Parser.compensateJumps(roll_gnss, jumpIdxRoll_gnss);
                        obj.dataStruct.GNSSdata.Attitude(:,2) = roll_gnss;
                    end
                    if ~isempty(jumpIdxYaw_gnss)
                        warning('GNSSdata.Attitude偏航角存在突变野值，共检测到%d个突变点', numel(jumpIdxYaw_gnss));
                        yaw_gnss = BB00Parser.compensateJumps(yaw_gnss, jumpIdxYaw_gnss);
                        obj.dataStruct.GNSSdata.Attitude(:,3) = yaw_gnss;
                    end
                end
            end
        end

        function fixSpecialGnssTimeJumps(obj, period)
            % 检查每一个周内秒的差分，是否存在-0.195的点，记为A点
            % 对于每个A点，找到其后第一个diff不等于周期的点B，
            % 检查B点的diff是否为0.205，且A、B相隔200
            % 满足条件则将A和B之间的周内秒加0.200
            if nargin < 2
                period = 0.005;
            end
            gnss_ms = obj.dataStruct.Time.GNSSMilliseconds ./ 1000; % 周内秒，单位秒
            diff_gnss = diff(gnss_ms);
            idxA = find(abs(diff_gnss + 0.195) < 1e-6);
            for i = 1:length(idxA)
                a = idxA(i);
                % 在a之后找第一个diff不等于period的点
                b = a + find(abs(diff_gnss(a+1:end) - period) > 1e-6, 1, 'first');
                if isempty(b)
                    continue;
                end
                % 检查b的diff是否为0.205，且b-a==200
                if abs(diff_gnss(b) - 0.205) < 1e-6 && (b - a == 200)
                    obj.dataStruct.Time.GNSSMilliseconds(a+1:b,:) = obj.dataStruct.Time.GNSSMilliseconds(a+1:b,:) + 200;
                    fprintf('已修正区间：第%d到第%d帧的GNSS毫秒(+0.200s)\n', a+1, b);
                end
            end
        end
    end

    methods (Static)
        function vec = compensateJumps(vec, jumpIdx)
            % 静态方法：对突变点进行补偿
            for i = 1:length(jumpIdx)
                idx = jumpIdx(i);
                % 找左侧最近的非突变点
                left = idx-1;
                while left >= 1 && ismember(left, jumpIdx)
                    left = left-1;
                end
                % 找右侧最近的非突变点
                right = idx+1;
                while right <= length(vec) && ismember(right, jumpIdx)
                    right = right+1;
                end
                if left >= 1 && right <= length(vec)
                    vec(idx) = mean([vec(left), vec(right)]);
                elseif left >= 1
                    vec(idx) = vec(left);
                elseif right <= length(vec)
                    vec(idx) = vec(right);
                end
            end
        end
        
        function yaw = headingToYaw(heading)
            % headingToYaw - 静态方法，将航向角(0~360°，顺时针为正)转换为偏航角(-180~180°，逆时针为正)
            % 输入: heading - 航向角，单位度，范围0~360，顺时针为正
            % 输出: yaw - 偏航角，单位度，范围-180~180，逆时针为正
            % 变换关系：yaw = -heading，结果归一化到[-180,180]
            yaw = -mod(heading, 360); % 先取负，保证方向
            idx = yaw < -180;
            yaw(idx) = yaw(idx) + 360;
            idx = yaw > 180;
            yaw(idx) = yaw(idx) - 360;
        end
    end
end
