function plotRinexFrameRate(details)
% plotRinexFrameRate 绘制RINEX文件时间戳和丢帧分析图
%
% 输入:
%   details - 由checkRinexFrameRate函数返回的详细信息结构体
%
% 示例:
%   [~, details] = checkRinexFrameRate('data.25O');
%   plotRinexFrameRate(details);

% 创建新的图形窗口
figure('Name', 'RINEX文件时间戳分析', 'NumberTitle', 'off', 'Position', [100, 100, 1200, 800]);

% 创建子图1：时间间隔随时间的变化
subplot(2, 2, 1);
plot(details.timestamps(1:end-1), details.actualIntervals, 'b-', 'LineWidth', 1);
hold on;

% 标记所有异常点
if ~isempty(details.anomalies.indices)
    plot(details.timestamps(details.anomalies.indices), details.anomalies.values, 'ro', 'MarkerSize', 6, 'LineWidth', 2);
end

% 特别标记丢帧点
if isfield(details, 'droppedFrames') && ~isempty(details.droppedFrames.locations)
    plot(details.timestamps(details.droppedFrames.locations), ...
         details.actualIntervals(details.droppedFrames.locations), ...
         'mo', 'MarkerSize', 8, 'LineWidth', 2);
end

% 绘制预期间隔的水平线
plot([details.timestamps(1), details.timestamps(end)], [details.expectedInterval, details.expectedInterval], 'g--', 'LineWidth', 2);
hold off;
title('时间间隔随时间的变化');
xlabel('时间 (秒)');
ylabel('时间间隔 (秒)');
grid on;

% 根据是否有丢帧调整图例
if isfield(details, 'droppedFrames') && ~isempty(details.droppedFrames.locations)
    legend('实际间隔', '异常点', '丢帧点', '预期间隔');
else
    legend('实际间隔', '异常点', '预期间隔');
end

% 创建子图2：时间间隔的直方图
subplot(2, 2, 2);
histogram(details.actualIntervals, min(100, length(unique(details.actualIntervals))));
hold on;
% 绘制预期间隔的垂直线
yLimits = ylim;
plot([details.expectedInterval, details.expectedInterval], [0, yLimits(2)], 'r--', 'LineWidth', 2);
hold off;
title('时间间隔分布直方图');
xlabel('时间间隔 (秒)');
ylabel('频数');
grid on;

% 创建子图3：时间戳随索引的变化
subplot(2, 2, 3);
plot(1:length(details.timestamps), details.timestamps, 'b-', 'LineWidth', 1);
hold on;

% 在时间戳图上标记丢帧位置
if isfield(details, 'droppedFrames') && ~isempty(details.droppedFrames.locations)
    % 在丢帧位置绘制垂直线
    for i = 1:length(details.droppedFrames.locations)
        idx = details.droppedFrames.locations(i);
        plot([idx, idx], [min(details.timestamps), max(details.timestamps)], 'm--', 'LineWidth', 1);
    end
end

hold off;
title('时间戳随索引的变化（垂直线表示丢帧位置）');
xlabel('数据点索引');
ylabel('时间 (秒)');
grid on;

% 创建子图4：丢帧分布图或时间间隔的箱线图
subplot(2, 2, 4);

% 如果有丢帧，绘制丢帧分布图
if isfield(details, 'droppedFrames') && details.droppedFrames.count > 0
    % 创建丢帧数量的饼图
    pie([details.droppedFrames.count, length(details.actualIntervals) + details.droppedFrames.count - details.droppedFrames.count]);
    title('数据完整性分析');
    legend({'丢失帧', '有效帧'}, 'Location', 'southoutside');
    
    % 添加丢帧信息文本
    text(0, 1.2, sprintf('总丢帧数: %d', details.droppedFrames.count), ...
        'HorizontalAlignment', 'center', 'FontWeight', 'bold');
    text(0, 1.1, sprintf('丢帧发生次数: %d', length(details.droppedFrames.locations)), ...
        'HorizontalAlignment', 'center');
    
    % 如果丢帧次数不多，显示每次丢帧的帧数
    if length(details.droppedFrames.locations) <= 5
        for i = 1:length(details.droppedFrames.locations)
            text(0, 1.0 - i*0.1, sprintf('位置 %d: 丢失 %d 帧', ...
                details.droppedFrames.locations(i), details.droppedFrames.framesPerDrop(i)), ...
                'HorizontalAlignment', 'center');
        end
    end
else
    % 如果没有丢帧，绘制时间间隔的箱线图
    boxplot(details.actualIntervals);
    title('时间间隔箱线图');
    ylabel('时间间隔 (秒)');
    grid on;
end

% 添加总体信息文本框
if isfield(details, 'droppedFrames')
    annotation('textbox', [0.1, 0.01, 0.8, 0.05], 'String', ...
        sprintf(['统计信息: 最小值=%.3f, 最大值=%.3f, 平均值=%.3f, 标准差=%.3f\n', ...
                 '预期间隔: %.3f, 异常点: %d (%.2f%%), 丢帧数: %d, 丢帧率: %.2f%%'], ...
                 details.statistics.min, details.statistics.max, ...
                 details.statistics.mean, details.statistics.std, ...
                 details.expectedInterval, length(details.anomalies.indices), ...
                 100 * length(details.anomalies.indices) / length(details.actualIntervals), ...
                 details.droppedFrames.count, ...
                 100 * details.droppedFrames.count / (length(details.actualIntervals) + details.droppedFrames.count)), ...
        'EdgeColor', 'none', 'HorizontalAlignment', 'center');
else
    annotation('textbox', [0.1, 0.01, 0.8, 0.05], 'String', ...
        sprintf(['统计信息: 最小值=%.3f, 最大值=%.3f, 平均值=%.3f, 标准差=%.3f\n', ...
                 '预期间隔: %.3f, 异常点数量: %d (%.2f%%)'], ...
                 details.statistics.min, details.statistics.max, ...
                 details.statistics.mean, details.statistics.std, ...
                 details.expectedInterval, length(details.anomalies.indices), ...
                 100 * length(details.anomalies.indices) / length(details.actualIntervals)), ...
        'EdgeColor', 'none', 'HorizontalAlignment', 'center');
end

end
