# GNRMC数据提取与分析工具

## 概述

本工具用于从串口接收的原始文本文件中提取GNRMC协议数据帧，并分析GNRMC时间戳与本地接收时间的差异。

## 文件说明

- `GNRMCAnalyzer.m` - 主要的GNRMC分析类
- `example_gnrmc_analysis.m` - 使用示例脚本
- `GNRMC_README.md` - 本说明文件

## 功能特性

1. **数据提取**
   - 从原始文本文件中识别时间戳标记（如 `[16:13:22.841]Rx:`）
   - 提取对应的GNRMC协议数据帧
   - 解析GNRMC帧中的UTC时间字段

2. **时间分析**
   - 考虑时区偏移（默认东八区UTC+8）计算本地接收时间与GNRMC UTC时间的差异
   - 处理跨天时间计算问题
   - 检测时间序列的连续性，识别时间跳变
   - 提供统计分析（均值、标准差、最大值、最小值、中位数）

3. **可视化**
   - 时间差序列图（已考虑时区偏移）
   - 时间差分布直方图
   - 本地时间和UTC时间序列图（标记跳变点）
   - 时间间隔分析图

4. **数据保存**
   - 将分析结果保存为MAT文件
   - 保留原始数据和分析结果

## 使用方法

### 基本使用

```matlab
% 创建分析器实例（使用东八区时间）
analyzer = GNRMCAnalyzer('your_data_file.txt', 8);

% 或者使用其他时区（例如UTC时间）
% analyzer = GNRMCAnalyzer('your_data_file.txt', 0);

% 获取提取的数据
data = analyzer.getExtractedData();

% 获取时间分析结果
analysis = analyzer.getTimeAnalysis();

% 绘制分析图表
analyzer.plotTimeDifferences();

% 保存结果
analyzer.saveResults();
```

### 数据文件格式

输入文件应包含以下格式的数据：

```
[16:13:22.841]Rx:
$GNRMC,081325.00,A,2245.02702863,N,11347.63257941,E,0.008,151.7,090725,3.3,W,A,C*5A

[16:13:23.842]Rx:
$GNRMC,081326.00,A,2245.02702863,N,11347.63257941,E,0.008,151.7,090725,3.3,W,A,C*5B
```

其中：
- `[HH:MM:SS.sss]Rx:` 表示本地接收时间戳
- `$GNRMC,hhmmss.ss,...` 表示GNRMC协议数据帧，其中 `hhmmss.ss` 是UTC时间

## 类方法说明

### 公共方法

- `GNRMCAnalyzer(filename, timeZoneOffset)` - 构造函数，自动执行数据提取和分析
  - `filename` - 数据文件路径
  - `timeZoneOffset` - 时区偏移（小时），默认为8（东八区）
- `getExtractedData()` - 获取提取的GNRMC数据
- `getTimeAnalysis()` - 获取时间差分析结果
- `plotTimeDifferences()` - 绘制时间差分析图表
- `saveResults(outputFilename)` - 保存分析结果到MAT文件
- `printAnalysisResults()` - 打印详细的分析结果

### 数据结构

#### extractedData 结构
```matlab
extractedData = struct(
    'localTimestamps',   % 本地时间戳数组（秒）
    'utcTimestamps',     % UTC时间戳数组（秒）
    'rawFrames',         % 原始GNRMC帧字符串数组
    'localTimeStrings',  % 本地时间字符串数组
    'utcTimeStrings'     % UTC时间字符串数组
);
```

#### timeAnalysis 结构
```matlab
timeAnalysis = struct(
    'timeDifferences',        % 时间差数组（秒，已考虑时区）
    'adjustedUtcTimestamps',  % 调整后的UTC时间戳（已转换为本地时区）
    'meanTimeDiff',           % 平均时间差
    'stdTimeDiff',            % 时间差标准差
    'maxTimeDiff',            % 最大时间差
    'minTimeDiff',            % 最小时间差
    'medianTimeDiff',         % 时间差中位数
    'localTimeJumps',         % 本地时间跳变点索引
    'utcTimeJumps'            % UTC时间跳变点索引
);
```

## 运行示例

1. 运行示例脚本：
   ```matlab
   run('example_gnrmc_analysis.m')
   ```

2. 如果没有数据文件，脚本会自动创建示例数据文件

3. 查看分析结果和图表

## 注意事项

1. **文件编码**：确保输入文件使用UTF-8编码
2. **时间格式**：
   - 本地时间格式：`HH:MM:SS.sss`
   - UTC时间格式：`hhmmss.ss`
3. **跨天处理**：工具会自动处理跨天的时间计算问题
4. **数据质量**：确保GNRMC帧格式正确，包含有效的校验和

## 错误处理

- 文件不存在或无法读取时会显示错误信息
- GNRMC帧格式不正确时会跳过该帧
- 时间解析失败时会跳过该数据点

## 扩展功能

可以根据需要扩展以下功能：
1. 支持其他NMEA协议（如GPGGA、GPGSV等）
2. 添加更多时间分析指标
3. 支持实时数据处理
4. 添加数据质量评估功能

## 技术支持

如有问题或建议，请联系开发团队。
