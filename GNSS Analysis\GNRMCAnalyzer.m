classdef GNRMCAnalyzer < handle
    % GNRMCAnalyzer - GNRMC协议数据提取与分析类
    % 从串口接收的原始文本文件中提取GNRMC数据帧，并分析时间差
    
    properties (Access = private)
        filename        % 原始数据文件名
        rawText         % 原始文本内容
        gnrmcData       % 提取的GNRMC数据
    end
    
    properties (Access = public)
        extractedFrames % 提取的数据帧数量
        timeAnalysis    % 时间差分析结果
        timeZoneOffset  % 时区偏移（小时），默认为东八区(+8)
    end
    
    methods
        function obj = GNRMCAnalyzer(filename, timeZoneOffset)
            % GNRMCAnalyzer - 构造函数
            % 输入:
            %   filename - 包含GNRMC数据的原始文本文件名
            %   timeZoneOffset - 时区偏移（小时），默认为东八区(+8)
            if nargin > 0
                obj.filename = filename;
                if nargin > 1
                    obj.timeZoneOffset = timeZoneOffset;
                else
                    obj.timeZoneOffset = 8; % 默认东八区
                end
                obj.loadRawData();
                obj.extractGNRMCFrames();
                obj.analyzeTimeDifferences();
            else
                obj.timeZoneOffset = 8; % 默认东八区
            end
        end
        
        function data = getExtractedData(obj)
            % getExtractedData - 获取提取的GNRMC数据
            % 输出:
            %   data - 包含所有提取数据的结构体
            data = obj.gnrmcData;
        end
        
        function analysis = getTimeAnalysis(obj)
            % getTimeAnalysis - 获取时间差分析结果
            % 输出:
            %   analysis - 时间差分析结果结构体
            analysis = obj.timeAnalysis;
        end
        
        function plotTimeDifferences(obj)
            % plotTimeDifferences - 绘制时间差分析图
            if isempty(obj.timeAnalysis)
                error('没有时间差分析数据，请先运行分析。');
            end

            figure('Name', 'GNRMC时间差分析', 'Position', [100, 100, 1400, 1000]);

            % 子图1: 时间差序列
            subplot(3, 2, 1);
            plot(obj.timeAnalysis.timeDifferences, 'b.-', 'LineWidth', 1.5);
            grid on;
            title('GNRMC时间戳与本地接收时间差（已考虑东八区）');
            xlabel('数据帧序号');
            ylabel('时间差 (秒)');

            % 子图2: 时间差直方图
            subplot(3, 2, 2);
            histogram(obj.timeAnalysis.timeDifferences, 30, 'FaceColor', 'cyan');
            grid on;
            title('时间差分布直方图');
            xlabel('时间差 (秒)');
            ylabel('频次');

            % 子图3: 本地接收时间序列及跳变检测
            subplot(3, 2, 3);
            plot(obj.gnrmcData.localTimestamps, 'r.-', 'LineWidth', 1.5);
            hold on;
            if isfield(obj.timeAnalysis, 'localTimeJumps') && ~isempty(obj.timeAnalysis.localTimeJumps)
                plot(obj.timeAnalysis.localTimeJumps, obj.gnrmcData.localTimestamps(obj.timeAnalysis.localTimeJumps), ...
                     'ro', 'MarkerSize', 8, 'MarkerFaceColor', 'red');
            end
            grid on;
            title('本地接收时间序列（红圈标示跳变点）');
            xlabel('数据帧序号');
            ylabel('本地时间 (秒)');
            hold off;

            % 子图4: GNRMC UTC时间序列及跳变检测
            subplot(3, 2, 4);
            plot(obj.gnrmcData.utcTimestamps, 'g.-', 'LineWidth', 1.5);
            hold on;
            if isfield(obj.timeAnalysis, 'utcTimeJumps') && ~isempty(obj.timeAnalysis.utcTimeJumps)
                plot(obj.timeAnalysis.utcTimeJumps, obj.gnrmcData.utcTimestamps(obj.timeAnalysis.utcTimeJumps), ...
                     'go', 'MarkerSize', 8, 'MarkerFaceColor', 'green');
            end
            grid on;
            title('GNRMC UTC时间序列（绿圈标示跳变点）');
            xlabel('数据帧序号');
            ylabel('UTC时间 (秒)');
            hold off;

            % 子图5: 本地时间间隔
            subplot(3, 2, 5);
            if length(obj.gnrmcData.localTimestamps) > 1
                localIntervals = diff(obj.gnrmcData.localTimestamps);
                plot(localIntervals, 'm.-', 'LineWidth', 1.5);
                grid on;
                title('本地时间间隔');
                xlabel('数据帧序号');
                ylabel('时间间隔 (秒)');
            end

            % 子图6: UTC时间间隔
            subplot(3, 2, 6);
            if length(obj.gnrmcData.utcTimestamps) > 1
                utcIntervals = diff(obj.gnrmcData.utcTimestamps);
                plot(utcIntervals, 'k.-', 'LineWidth', 1.5);
                grid on;
                title('UTC时间间隔');
                xlabel('数据帧序号');
                ylabel('时间间隔 (秒)');
            end

            % 显示统计信息
            obj.printAnalysisResults();
        end
        
        function saveResults(obj, outputFilename)
            % saveResults - 保存分析结果到MAT文件
            % 输入:
            %   outputFilename - 输出文件名(可选)
            if nargin < 2
                [~, name, ~] = fileparts(obj.filename);
                outputFilename = [name '_gnrmc_analysis.mat'];
            end
            
            gnrmcData = obj.gnrmcData;
            timeAnalysis = obj.timeAnalysis;
            extractedFrames = obj.extractedFrames;
            
            save(outputFilename, 'gnrmcData', 'timeAnalysis', 'extractedFrames', '-v7.3');
            fprintf('分析结果已保存到: %s\n', outputFilename);
        end

        function printAnalysisResults(obj)
            % printAnalysisResults - 打印详细的分析结果
            fprintf('\n=== GNRMC时间差分析统计 ===\n');
            fprintf('时区设置: 东八区 (UTC%+d)\n', obj.timeZoneOffset);
            fprintf('提取的GNRMC帧数: %d\n', obj.extractedFrames);

            if ~isempty(obj.timeAnalysis)
                fprintf('平均时间差: %.6f 秒\n', obj.timeAnalysis.meanTimeDiff);
                fprintf('时间差标准差: %.6f 秒\n', obj.timeAnalysis.stdTimeDiff);
                fprintf('最大时间差: %.6f 秒\n', obj.timeAnalysis.maxTimeDiff);
                fprintf('最小时间差: %.6f 秒\n', obj.timeAnalysis.minTimeDiff);
                fprintf('时间差中位数: %.6f 秒\n', obj.timeAnalysis.medianTimeDiff);

                % 时间连续性分析结果
                if isfield(obj.timeAnalysis, 'localTimeJumps')
                    fprintf('\n=== 时间连续性分析 ===\n');
                    fprintf('本地时间跳变点数量: %d\n', length(obj.timeAnalysis.localTimeJumps));
                    fprintf('UTC时间跳变点数量: %d\n', length(obj.timeAnalysis.utcTimeJumps));

                    if ~isempty(obj.timeAnalysis.localTimeJumps)
                        fprintf('本地时间跳变位置: ');
                        fprintf('%d ', obj.timeAnalysis.localTimeJumps);
                        fprintf('\n');
                    end

                    if ~isempty(obj.timeAnalysis.utcTimeJumps)
                        fprintf('UTC时间跳变位置: ');
                        fprintf('%d ', obj.timeAnalysis.utcTimeJumps);
                        fprintf('\n');
                    end
                end
            end
        end
    end
    
    methods (Access = private)
        function loadRawData(obj)
            % loadRawData - 加载原始文本文件
            try
                fid = fopen(obj.filename, 'r', 'n', 'UTF-8');
                if fid == -1
                    error('无法打开文件: %s', obj.filename);
                end
                obj.rawText = fread(fid, '*char')';
                fclose(fid);
                fprintf('成功加载文件: %s\n', obj.filename);
            catch ME
                error('读取文件时出错: %s', ME.message);
            end
        end
        
        function extractGNRMCFrames(obj)
            % extractGNRMCFrames - 从原始文本中提取GNRMC数据帧
            
            % 初始化存储结构
            obj.gnrmcData = struct();
            obj.gnrmcData.localTimestamps = [];
            obj.gnrmcData.utcTimestamps = [];
            obj.gnrmcData.rawFrames = {};
            obj.gnrmcData.localTimeStrings = {};
            obj.gnrmcData.utcTimeStrings = {};
            
            % 分割文本为行
            lines = strsplit(obj.rawText, '\n');
            
            frameCount = 0;
            currentLocalTime = '';
            
            for i = 1:length(lines)
                line = strtrim(lines{i});
                
                % 检查是否为时间戳行 [HH:MM:SS.sss]Rx:
                timestampMatch = regexp(line, '^\[(\d{2}:\d{2}:\d{2}\.\d{3})\]Rx:', 'tokens');
                if ~isempty(timestampMatch)
                    currentLocalTime = timestampMatch{1}{1};
                    continue;
                end
                
                % 检查是否为GNRMC数据帧
                if contains(line, '$GNRMC') && ~isempty(currentLocalTime)
                    % 提取完整的GNRMC帧
                    gnrmcMatch = regexp(line, '\$GNRMC,[^*]*\*[0-9A-F]{2}', 'match');
                    if ~isempty(gnrmcMatch)
                        gnrmcFrame = gnrmcMatch{1};
                        
                        % 解析GNRMC帧获取UTC时间
                        utcTime = obj.parseGNRMCTime(gnrmcFrame);
                        if ~isempty(utcTime)
                            frameCount = frameCount + 1;
                            
                            % 存储数据
                            obj.gnrmcData.rawFrames{frameCount} = gnrmcFrame;
                            obj.gnrmcData.localTimeStrings{frameCount} = currentLocalTime;
                            obj.gnrmcData.utcTimeStrings{frameCount} = utcTime;
                            
                            % 转换时间为数值（秒）
                            obj.gnrmcData.localTimestamps(frameCount) = obj.timeStringToSeconds(currentLocalTime);
                            obj.gnrmcData.utcTimestamps(frameCount) = obj.timeStringToSeconds(utcTime);
                        end
                    end
                end
            end
            
            obj.extractedFrames = frameCount;
            fprintf('成功提取 %d 个GNRMC数据帧\n', frameCount);
        end
        
        function utcTime = parseGNRMCTime(~, gnrmcFrame)
            % parseGNRMCTime - 从GNRMC帧中解析UTC时间
            % 输入:
            %   gnrmcFrame - GNRMC数据帧字符串
            % 输出:
            %   utcTime - UTC时间字符串 (hhmmss.ss格式)

            % 分割GNRMC字段
            fields = strsplit(gnrmcFrame, ',');

            % UTC时间在第2个字段（索引2）
            if length(fields) >= 2
                utcTime = fields{2};
                % 验证时间格式 (hhmmss.ss)
                if ~isempty(regexp(utcTime, '^\d{6}\.\d{2}$', 'once'))
                    return;
                end
            end

            utcTime = '';
        end

        function seconds = timeStringToSeconds(~, timeStr)
            % timeStringToSeconds - 将时间字符串转换为秒数
            % 输入:
            %   timeStr - 时间字符串，格式为 'HH:MM:SS.sss' 或 'hhmmss.ss'
            % 输出:
            %   seconds - 对应的秒数
            
            if contains(timeStr, ':')
                % 本地时间格式 'HH:MM:SS.sss'
                parts = strsplit(timeStr, ':');
                hours = str2double(parts{1});
                minutes = str2double(parts{2});
                secParts = strsplit(parts{3}, '.');
                secs = str2double(secParts{1});
                if length(secParts) > 1
                    millisecs = str2double(secParts{2}) / 1000;
                else
                    millisecs = 0;
                end
                seconds = hours * 3600 + minutes * 60 + secs + millisecs;
            else
                % UTC时间格式 'hhmmss.ss'
                if length(timeStr) >= 6
                    hours = str2double(timeStr(1:2));
                    minutes = str2double(timeStr(3:4));
                    secs = str2double(timeStr(5:end));
                    seconds = hours * 3600 + minutes * 60 + secs;
                else
                    seconds = NaN;
                end
            end
        end
        
        function analyzeTimeDifferences(obj)
            % analyzeTimeDifferences - 分析GNRMC时间戳与本地接收时间的差异

            if isempty(obj.gnrmcData.localTimestamps) || isempty(obj.gnrmcData.utcTimestamps)
                warning('没有足够的时间数据进行分析');
                return;
            end

            % 将UTC时间转换为本地时间（考虑时区偏移）
            utcToLocalOffset = obj.timeZoneOffset * 3600; % 转换为秒
            adjustedUtcTimestamps = obj.gnrmcData.utcTimestamps + utcToLocalOffset;

            % 处理UTC时间跨天的情况
            adjustedUtcTimestamps = mod(adjustedUtcTimestamps, 24*3600);

            % 计算时间差（本地时间 - 调整后的UTC时间）
            timeDiffs = obj.gnrmcData.localTimestamps - adjustedUtcTimestamps;

            % 处理跨天的情况
            % 如果时间差超过12小时，可能是跨天造成的
            timeDiffs(timeDiffs > 12*3600) = timeDiffs(timeDiffs > 12*3600) - 24*3600;
            timeDiffs(timeDiffs < -12*3600) = timeDiffs(timeDiffs < -12*3600) + 24*3600;

            % 时间连续性分析
            [localJumps, utcJumps] = obj.detectTimeJumps();

            % 存储分析结果
            obj.timeAnalysis = struct();
            obj.timeAnalysis.timeDifferences = timeDiffs;
            obj.timeAnalysis.adjustedUtcTimestamps = adjustedUtcTimestamps;
            obj.timeAnalysis.meanTimeDiff = mean(timeDiffs);
            obj.timeAnalysis.stdTimeDiff = std(timeDiffs);
            obj.timeAnalysis.maxTimeDiff = max(timeDiffs);
            obj.timeAnalysis.minTimeDiff = min(timeDiffs);
            obj.timeAnalysis.medianTimeDiff = median(timeDiffs);
            obj.timeAnalysis.localTimeJumps = localJumps;
            obj.timeAnalysis.utcTimeJumps = utcJumps;

            fprintf('时间差分析完成（已考虑东八区时差）\n');
        end

        function [localJumps, utcJumps] = detectTimeJumps(obj)
            % detectTimeJumps - 检测时间序列中的跳变
            % 输出:
            %   localJumps - 本地时间跳变的索引
            %   utcJumps - UTC时间跳变的索引

            localJumps = [];
            utcJumps = [];

            if length(obj.gnrmcData.localTimestamps) < 3
                return;
            end

            % 检测本地时间跳变
            localIntervals = diff(obj.gnrmcData.localTimestamps);
            medianLocalInterval = median(localIntervals);

            % 定义跳变阈值（超过中位数间隔的3倍认为是跳变）
            localThreshold = max(0.1, 3 * medianLocalInterval); % 最小阈值0.1秒
            localJumpIdx = find(abs(localIntervals - medianLocalInterval) > localThreshold);
            localJumps = localJumpIdx + 1; % 跳变发生在下一个点

            % 检测UTC时间跳变
            utcIntervals = diff(obj.gnrmcData.utcTimestamps);

            % 处理UTC时间跨天的情况
            utcIntervals(utcIntervals < -12*3600) = utcIntervals(utcIntervals < -12*3600) + 24*3600;
            utcIntervals(utcIntervals > 12*3600) = utcIntervals(utcIntervals > 12*3600) - 24*3600;

            medianUtcInterval = median(utcIntervals);
            utcThreshold = max(0.1, 3 * medianUtcInterval); % 最小阈值0.1秒
            utcJumpIdx = find(abs(utcIntervals - medianUtcInterval) > utcThreshold);
            utcJumps = utcJumpIdx + 1; % 跳变发生在下一个点
        end
    end
end
