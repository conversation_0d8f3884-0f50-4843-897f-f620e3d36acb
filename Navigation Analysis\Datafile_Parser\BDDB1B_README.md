# BDDB1BParser 使用说明

## 概述

BDDB1BParser 是一个用于解析 BDDB1B 协议 CSV 文件的 MATLAB 类，仿照 BB00Parser 的结构设计。该类能够自动解析包含 31 列数据的 BDDB1B 协议文件，并将数据组织成分层的结构体格式。

## 协议头信息

- **协议头**: BDDB1B
- **数据列数**: 31 列
- **数据格式**: CSV

## 数据列定义

| 列号 | 字段名称 | 单位 | 说明 |
|------|----------|------|------|
| 1 | 本地日期 | - | 本地日期时间 |
| 2 | 横滚角 | ° | 横滚角度 |
| 3 | 俯仰角 | ° | 俯仰角度 |
| 4 | 方位角 | ° | 方位角度 |
| 5 | 陀螺X | °/s | X轴角速度 |
| 6 | 陀螺Y | °/s | Y轴角速度 |
| 7 | 陀螺Z | °/s | Z轴角速度 |
| 8 | 加速度X | m/s² | X轴加速度 |
| 9 | 加速度Y | m/s² | Y轴加速度 |
| 10 | 加速度Z | m/s² | Z轴加速度 |
| 11 | 经度 | ° | 经度坐标 |
| 12 | 纬度 | ° | 纬度坐标 |
| 13 | 高度 | m | 高度信息 |
| 14 | 东向速度 | m/s | 东向速度分量 |
| 15 | 北向速度 | m/s | 北向速度分量 |
| 16 | 天向速度 | m/s | 天向速度分量 |
| 17 | 状态 | - | 系统状态 |
| 18 | 周内秒 | s | GNSS周内秒 |
| 19 | GNSS周 | - | GNSS周数 |
| 20 | 时间戳 | s | 时间戳 |
| 21 | GPS日期 | - | GPS日期 |
| 22 | 轮询类型 | - | 轮询类型 |
| 23 | PpsDelay | - | PPS延迟 |
| 24 | 内部温度 | ° | 内部温度 |
| 25 | GPS定位状态 | - | GPS定位状态 |
| 26 | 卫星数 | - | 可见卫星数量 |
| 27 | GPS定向状态 | - | GPS定向状态 |
| 28 | 基线长度 | - | 基线长度 |
| 29 | 轮速 | - | 轮速信息 |
| 30 | RTK状态 | - | RTK定位状态 |
| 31 | 跑车标定状态 | - | 跑车标定状态 |

## 数据结构

解析后的数据存储在 `dataStruct` 属性中，包含以下子结构：

### IMU 数据
```matlab
dataStruct.IMU.Gyro.X/Y/Z     % 陀螺仪数据 (°/s)
dataStruct.IMU.Accel.X/Y/Z    % 加速度计数据 (m/s²)
```

### 姿态数据
```matlab
dataStruct.Attitude.Roll      % 横滚角 (°)
dataStruct.Attitude.Pitch     % 俯仰角 (°)
dataStruct.Attitude.Yaw       % 方位角 (°)
```

### 位置数据
```matlab
dataStruct.Position.Longitude % 经度 (°)
dataStruct.Position.Latitude  % 纬度 (°)
dataStruct.Position.Altitude  % 高度 (m)
```

### 速度数据
```matlab
dataStruct.Velocity.East      % 东向速度 (m/s)
dataStruct.Velocity.North     % 北向速度 (m/s)
dataStruct.Velocity.Up        % 天向速度 (m/s)
```

### 时间数据
```matlab
dataStruct.Time.GNSSWeek      % GNSS周
dataStruct.Time.GNSSSeconds   % 周内秒 (s)
dataStruct.Time.Timestamp     % 时间戳 (s)
```

### 卫星信息
```matlab
dataStruct.SatInfo.SatelliteCount        % 卫星数
dataStruct.SatInfo.RTKStatus             % RTK状态
dataStruct.SatInfo.GPSPositionStatus     % GPS定位状态
dataStruct.SatInfo.GPSOrientationStatus  % GPS定向状态
dataStruct.SatInfo.BaselineLength        % 基线长度
```

### 其他信息
```matlab
dataStruct.Other.LocalDate               % 本地日期
dataStruct.Other.Status                  % 状态
dataStruct.Other.GPSDate                 % GPS日期
dataStruct.Other.PollingType             % 轮询类型
dataStruct.Other.PpsDelay                % PPS延迟
dataStruct.Other.InternalTemperature     % 内部温度
dataStruct.Other.WheelSpeed              % 轮速
dataStruct.Other.VehicleCalibrationStatus % 跑车标定状态
```

## 使用方法

### 基本使用
```matlab
% 创建解析器对象并自动解析
parser = BDDB1BParser('your_bddb1b_file.csv');

% 获取解析后的数据
data = parser.getData();

% 查看数据输出频率
fprintf('数据输出频率: %.2f Hz\n', parser.outputRate);
```

### 验证文件格式
```matlab
% 验证CSV文件列顺序
parser.validateColumns();
```

### 保存数据
```matlab
% 保存为MAT文件
parser.saveToMat('output_filename.mat');
```

### 转换为ComNavResult对象
```matlab
% 转换为ComNavResult对象（需要ComNavResult类）
navObj = parser.toComNavResult();
```

## 主要功能

1. **自动解析**: 构造函数中自动调用解析方法
2. **数据验证**: 检查列数是否匹配协议要求
3. **野值检测**: 自动检测和补偿数据中的野值和突变
4. **频率计算**: 自动计算数据输出频率
5. **格式转换**: 支持转换为ComNavResult对象格式
6. **数据保存**: 支持保存为MAT文件格式

## 野值检测

类会自动检测以下类型的野值：
- 经纬度超出物理范围 (±180°, ±90°)
- 高度异常值 (>10km)
- 速度异常值 (>100m/s)
- 姿态角异常值
- 数据突变点

检测到野值后会自动进行补偿处理。

## 注意事项

1. CSV文件必须包含31列数据，列顺序必须符合BDDB1B协议
2. 文件编码建议使用UTF-8
3. 转换为ComNavResult对象需要该类在MATLAB路径中
4. 建议在解析前备份原始数据文件

## 示例代码

参见 `BDDB1B_example.m` 文件，包含完整的使用示例和数据可视化代码。
