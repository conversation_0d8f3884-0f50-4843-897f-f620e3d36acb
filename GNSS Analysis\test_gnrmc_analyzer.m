%% GNRMC分析器测试脚本
% 用于测试GNRMCAnalyzer类的功能

%% 清理工作空间
clear; clc; close all;

%% 测试1: 创建测试数据文件
fprintf('=== 测试1: 创建测试数据文件 ===\n');

testFile = 'test_gnrmc_data.txt';
createTestDataFile(testFile);

%% 测试2: 基本功能测试
fprintf('\n=== 测试2: 基本功能测试 ===\n');

try
    % 创建分析器（使用东八区）
    analyzer = GNRMCAnalyzer(testFile, 8);

    % 检查提取的帧数
    assert(analyzer.extractedFrames > 0, '应该提取到GNRMC帧');
    fprintf('✓ 成功提取 %d 个GNRMC帧\n', analyzer.extractedFrames);
    
    % 检查数据结构
    data = analyzer.getExtractedData();
    assert(isstruct(data), '数据应该是结构体');
    assert(isfield(data, 'localTimestamps'), '应该包含本地时间戳');
    assert(isfield(data, 'utcTimestamps'), '应该包含UTC时间戳');
    fprintf('✓ 数据结构正确\n');
    
    % 检查时间分析
    analysis = analyzer.getTimeAnalysis();
    assert(isstruct(analysis), '分析结果应该是结构体');
    assert(isfield(analysis, 'timeDifferences'), '应该包含时间差');
    assert(~isnan(analysis.meanTimeDiff), '平均时间差应该是有效数值');
    fprintf('✓ 时间分析正确\n');
    
    fprintf('基本功能测试通过!\n');
    
catch ME
    fprintf('❌ 基本功能测试失败: %s\n', ME.message);
end

%% 测试3: 时间解析测试
fprintf('\n=== 测试3: 时间解析测试 ===\n');

try
    % 测试本地时间解析
    testAnalyzer = GNRMCAnalyzer();  % 创建空实例用于测试

    localTime1 = testAnalyzer.timeStringToSeconds('16:13:22.841');
    expected1 = 16*3600 + 13*60 + 22.841;
    assert(abs(localTime1 - expected1) < 0.001, '本地时间解析错误');
    fprintf('✓ 本地时间解析正确\n');

    % 测试UTC时间解析
    utcTime1 = testAnalyzer.timeStringToSeconds('081325.00');
    expected2 = 8*3600 + 13*60 + 25.00;
    assert(abs(utcTime1 - expected2) < 0.001, 'UTC时间解析错误');
    fprintf('✓ UTC时间解析正确\n');

    % 测试时区偏移
    assert(testAnalyzer.timeZoneOffset == 8, '默认时区应该是东八区');
    fprintf('✓ 时区设置正确\n');
    
    fprintf('时间解析测试通过!\n');
    
catch ME
    fprintf('❌ 时间解析测试失败: %s\n', ME.message);
end

%% 测试4: 边界条件测试
fprintf('\n=== 测试4: 边界条件测试 ===\n');

try
    % 测试空文件
    emptyFile = 'empty_test.txt';
    fid = fopen(emptyFile, 'w');
    fclose(fid);
    
    emptyAnalyzer = GNRMCAnalyzer(emptyFile);
    assert(emptyAnalyzer.extractedFrames == 0, '空文件应该提取0帧');
    fprintf('✓ 空文件处理正确\n');
    
    % 清理
    delete(emptyFile);
    
    fprintf('边界条件测试通过!\n');
    
catch ME
    fprintf('❌ 边界条件测试失败: %s\n', ME.message);
end

%% 测试5: 保存和加载测试
fprintf('\n=== 测试5: 保存和加载测试 ===\n');

try
    % 使用之前的分析器保存结果
    outputFile = 'test_results.mat';
    analyzer.saveResults(outputFile);
    
    % 检查文件是否存在
    assert(exist(outputFile, 'file') == 2, '结果文件应该存在');
    fprintf('✓ 结果保存成功\n');
    
    % 加载并验证
    loaded = load(outputFile);
    assert(isfield(loaded, 'gnrmcData'), '应该包含GNRMC数据');
    assert(isfield(loaded, 'timeAnalysis'), '应该包含时间分析');
    fprintf('✓ 结果加载成功\n');
    
    % 清理
    delete(outputFile);
    
    fprintf('保存和加载测试通过!\n');
    
catch ME
    fprintf('❌ 保存和加载测试失败: %s\n', ME.message);
end

%% 测试6: 可视化测试
fprintf('\n=== 测试6: 可视化测试 ===\n');

try
    % 测试绘图功能（不显示图形）
    set(0, 'DefaultFigureVisible', 'off');  % 关闭图形显示
    analyzer.plotTimeDifferences();
    set(0, 'DefaultFigureVisible', 'on');   % 恢复图形显示
    
    fprintf('✓ 可视化功能正常\n');
    fprintf('可视化测试通过!\n');
    
catch ME
    fprintf('❌ 可视化测试失败: %s\n', ME.message);
    set(0, 'DefaultFigureVisible', 'on');   % 确保恢复图形显示
end

%% 清理测试文件
delete(testFile);

%% 测试总结
fprintf('\n=== 测试总结 ===\n');
fprintf('所有测试完成。请检查上述输出确认各项功能正常。\n');

%% 辅助函数
function createTestDataFile(filename)
    % 创建测试用的GNRMC数据文件
    
    fid = fopen(filename, 'w', 'n', 'UTF-8');
    if fid == -1
        error('无法创建测试文件');
    end
    
    % 写入测试数据 - 模拟真实的串口数据
    testData = {
        '[08:13:20.100]Rx:'
        '$GNRMC,081320.00,A,2245.02702863,N,11347.63257941,E,0.008,151.7,090725,3.3,W,A,C*5A'
        ''
        '[08:13:21.101]Rx:'
        '$GNRMC,081321.00,A,2245.02702863,N,11347.63257941,E,0.008,151.7,090725,3.3,W,A,C*5B'
        ''
        '[08:13:22.102]Rx:'
        '$GNRMC,081322.00,A,2245.02702863,N,11347.63257941,E,0.008,151.7,090725,3.3,W,A,C*5C'
        ''
        '[08:13:23.103]Rx:'
        '$GNRMC,081323.00,A,2245.02702863,N,11347.63257941,E,0.008,151.7,090725,3.3,W,A,C*5D'
        ''
        '[08:13:24.104]Rx:'
        '$GNRMC,081324.00,A,2245.02702863,N,11347.63257941,E,0.008,151.7,090725,3.3,W,A,C*5E'
        ''
        '一些其他数据'
        '[08:13:25.105]Rx:'
        '$GNRMC,081325.00,A,2245.02702863,N,11347.63257941,E,0.008,151.7,090725,3.3,W,A,C*5F'
        ''
    };
    
    for i = 1:length(testData)
        fprintf(fid, '%s\n', testData{i});
    end
    
    fclose(fid);
    fprintf('测试数据文件已创建: %s\n', filename);
end
