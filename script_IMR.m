
IEprojectFolderpath = fullfile(projectFolderpath, "IE_project");
if ~exist(IEprojectFolderpath, "dir")
    mkdir(IEprojectFolderpath);
end

%% 检验MD01文件
% RinexFilepath = fullfile(projectFolderpath,"sd_data_1_20250707_102703.25O");
% analyzeRinexFrameRate(RinexFilepath, 1);

%% 生成IMR文件
imrobj = IMRFileGenerator(INSobj, fullfile(IEprojectFolderpath, "IMU_raw_data.imr"));
if ~exist(fullfile(IEprojectFolderpath, "IMU_raw_data.imr"),"file")
    imrobj.generateIMR;
end

%% 生成静止对准参数
alignParaFileSavePath = fullfile(IEprojectFolderpath,"静止对准参数.txt");
dms = vertcat(degrees2dms(INSobj.position(1,1)),...
degrees2dms(INSobj.position(1,2)),...
degrees2dms(INSobj.position(end,1)),...
degrees2dms(INSobj.position(end,2)));
writematrix(dms, alignParaFileSavePath, 'Delimiter', 'space', 'WriteMode','overwrite');
h = vertcat(INSobj.position(1,3),INSobj.position(end,3));
writematrix(h, alignParaFileSavePath, 'Delimiter', 'space', 'WriteMode','append');
att = vertcat(INSobj.attitude(1,3),INSobj.attitude(end,3));
writematrix(att, alignParaFileSavePath, 'Delimiter', 'space', 'WriteMode','append');

clear dms h att