classdef BDDB1BParser < DatafileParser
    % BDDB1BParser - 用于解析BDDB1B协议CSV文件并管理数据的类
    % 继承自DatafileParser抽象父类

    properties (Access = public)
        szImuName       = 'INS370-25J'
        szProgramName   = 'MD01'
        columnNames = { ...
            '本地日期', '横滚角(°)', '俯仰角(°)', '方位角(°)', '陀螺X(°/s)', '陀螺Y(°/s)', '陀螺Z(°/s)', ...
            '加速度X(m/s2)', '加速度Y(m/s2)', '加速度Z(m/s2)', '经度(°)', '纬度(°)', '高度(m)', ...
            '东向速度(m/s)', '北向速度(m/s)', '天向速度(m/s)', '状态', '周内秒(s)', 'GNSS周', ...
            '时间戳(s)', 'GPS日期', '轮询类型', 'PpsDelay', '内部温度(°)', 'GPS定位状态', ...
            '卫星数', 'GPS定向状态', '基线长度', '轮速', 'RTK状态', '跑车标定状态'};
    end

    methods
        function obj = BDDB1BParser(filename)
            % BDDB1BParser - 构造函数
            % 调用父类构造函数
            obj@DatafileParser(filename);
            obj = obj.fixSpecialGnssTimeJumps();
        end
    end

    methods (Access = protected)
        
        function parseCSV(obj)
            % parseCSV - 解析CSV文件并将数据存储到分层结构体中
            % 实现父类的抽象方法
            try
                % 使用父类的通用读取方法
                obj.readCSVFile();
                
                obj.dataStruct = struct();
                
                % IMU数据 (陀螺仪: 列5-7, 加速度计: 列8-10)
                obj.dataStruct.IMU = struct(...
                    'Gyro', struct(...
                        'X', obj.rawData{:,5}, ...
                        'Y', obj.rawData{:,6}, ...
                        'Z', obj.rawData{:,7}), ...
                    'Accel', struct(...
                        'X', obj.rawData{:,8}, ...
                        'Y', obj.rawData{:,9}, ...
                        'Z', obj.rawData{:,10}) ...
                );
                
                % 姿态数据 (横滚角: 列2, 俯仰角: 列3, 方位角: 列4)
                obj.dataStruct.Attitude = struct(...
                    'Roll', obj.rawData{:,2}, ...
                    'Pitch', obj.rawData{:,3}, ...
                    'Yaw', obj.rawData{:,4} ...
                );
                
                % 位置数据 (经度: 列11, 纬度: 列12, 高度: 列13)
                obj.dataStruct.Position = struct(...
                    'Longitude', obj.rawData{:,11}, ...
                    'Latitude', obj.rawData{:,12}, ...
                    'Altitude', obj.rawData{:,13} ...
                );
                
                % 速度数据 (东向: 列14, 北向: 列15, 天向: 列16)
                obj.dataStruct.Velocity = struct(...
                    'East', obj.rawData{:,14}, ...
                    'North', obj.rawData{:,15}, ...
                    'Up', obj.rawData{:,16} ...
                );
                
                % GNSS时间数据 (周内秒: 列18, GNSS周: 列19)
                obj.dataStruct.Time = struct(...
                    'GNSSWeek', obj.rawData{:,19}, ...
                    'GNSSSeconds', obj.rawData{:,18}, ...
                    'Timestamp', obj.rawData{:,20} ...
                );
                
                % 卫星与RTK信息 (卫星数: 列26, RTK状态: 列30)
                obj.dataStruct.SatInfo = struct(...
                    'SatelliteCount', obj.rawData{:,26}, ...
                    'RTKStatus', obj.rawData{:,30}, ...
                    'GPSPositionStatus', obj.rawData{:,25}, ...
                    'GPSOrientationStatus', obj.rawData{:,27}, ...
                    'BaselineLength', obj.rawData{:,28} ...
                );
                
                % 其他信息
                obj.dataStruct.Other = struct(...
                    'LocalDate', obj.rawData{:,1}, ...
                    'Status', obj.rawData{:,17}, ...
                    'GPSDate', obj.rawData{:,21}, ...
                    'PollingType', obj.rawData{:,22}, ...
                    'PpsDelay', obj.rawData{:,23}, ...
                    'InternalTemperature', obj.rawData{:,24}, ...
                    'WheelSpeed', obj.rawData{:,29}, ...
                    'VehicleCalibrationStatus', obj.rawData{:,31} ...
                );
                
                % 使用父类方法计算数据输出频率
                gnss_seconds = obj.rawData{:,18}; % 周内秒
                obj.calculateOutputRate(gnss_seconds, 's');
                
                % 野值检测
                obj.detectOutliersInNavData();

                disp('BDDB1B CSV文件解析完成，数据已存储在分层的dataStruct属性中。');
            catch ME
                error('解析BDDB1B CSV文件时出错: %s\n请检查文件格式和列顺序是否与BDDB1B协议一致。', ME.message);
            end
        end
    end

    methods (Access = private)
        function detectOutliersInNavData(obj)
            % detectOutliersInNavData - 检查组合导航解算数据中的野值，并补偿突变
            if ~isfield(obj.dataStruct, 'Position') || ~isfield(obj.dataStruct, 'Velocity') || ~isfield(obj.dataStruct, 'Attitude')
                warning('缺少组合导航解算数据，无法进行野值检测');
                return;
            end

            pos = obj.dataStruct.Position;
            vel = obj.dataStruct.Velocity;
            att = obj.dataStruct.Attitude;

            % 检查经纬高
            lon = pos.Longitude; lat = pos.Latitude; alt = pos.Altitude;
            if any(abs(lon) > 180) || any(abs(lat) > 90)
                warning('经纬度存在超出物理范围的野值');
            end
            if any(abs(alt) > 1e4)
                warning('高度存在异常野值(>10km)');
            end

            % 检查速度
            v = [vel.East, vel.North, vel.Up];
            if any(abs(v(:)) > 100)
                warning('速度存在异常野值(>100m/s)');
            end

            % 检查姿态
            pitch = att.Pitch; roll = att.Roll; yaw = att.Yaw;
            if any(abs(pitch) > 90) || any(abs(roll) > 180) || any(abs(yaw) > 360)
                warning('姿态角存在异常野值');
            end

            % 检查突变（差分大于阈值）并补偿
            [dLon, dLat, dAlt] = deal(abs(diff(lon)), abs(diff(lat)), abs(diff(alt)));
            jumpIdxLon = find(dLon > 0.01) + 1;
            jumpIdxLat = find(dLat > 0.01) + 1;
            jumpIdxAlt = find(dAlt > 10) + 1;
            jumpIdx = unique([jumpIdxLon; jumpIdxLat; jumpIdxAlt]);

            if ~isempty(jumpIdx)
                warning('经纬高存在突变野值，共检测到%d个突变点', numel(jumpIdx));
                lon = BDDB1BParser.compensateJumps(lon, jumpIdx);
                lat = BDDB1BParser.compensateJumps(lat, jumpIdx);
                alt = BDDB1BParser.compensateJumps(alt, jumpIdx);
                obj.dataStruct.Position.Longitude = lon;
                obj.dataStruct.Position.Latitude = lat;
                obj.dataStruct.Position.Altitude = alt;
            end

            dV = abs(diff(v));
            jumpIdxV = find(any(dV > 10,2)) + 1;
            if ~isempty(jumpIdxV)
                warning('速度存在突变野值，共检测到%d个突变点', numel(jumpIdxV));
                for k = 1:3
                    v(:,k) = BDDB1BParser.compensateJumps(v(:,k), jumpIdxV);
                end
                obj.dataStruct.Velocity.East = v(:,1);
                obj.dataStruct.Velocity.North = v(:,2);
                obj.dataStruct.Velocity.Up = v(:,3);
            end

            dPitch = abs(diff(pitch)); dRoll = abs(diff(roll)); dYaw = abs(diff(yaw));
            jumpIdxPitch = find(dPitch > 10) + 1;
            jumpIdxRoll = find(dRoll > 10) + 1;
            jumpIdxYaw = find(dYaw > 20) + 1;

            if ~isempty(jumpIdxPitch)
                warning('俯仰角存在突变野值，共检测到%d个突变点', numel(jumpIdxPitch));
                pitch = BDDB1BParser.compensateJumps(pitch, jumpIdxPitch);
                obj.dataStruct.Attitude.Pitch = pitch;
            end
            if ~isempty(jumpIdxRoll)
                warning('横滚角存在突变野值，共检测到%d个突变点', numel(jumpIdxRoll));
                roll = BDDB1BParser.compensateJumps(roll, jumpIdxRoll);
                obj.dataStruct.Attitude.Roll = roll;
            end
            if ~isempty(jumpIdxYaw)
                warning('偏航角存在突变野值，共检测到%d个突变点', numel(jumpIdxYaw));
                yaw = BDDB1BParser.compensateJumps(yaw, jumpIdxYaw);
                obj.dataStruct.Attitude.Yaw = yaw;
            end
        end

        function obj = fixSpecialGnssTimeJumps(obj, period)
            % 检查每一个周内秒的差分，是否存在-0.195的点，记为A点
            % 对于每个A点，找到其后第一个diff不等于周期的点B，
            % 检查B点的diff是否为0.205，且A、B相隔200
            % 满足条件则将A和B之间的周内秒加0.200
            if nargin < 2
                period = 0.005;
            end
            gnss_ms = obj.dataStruct.Time.GNSSSeconds; % 周内秒，单位秒
            diff_gnss = diff(gnss_ms);
            idxA = find(abs(diff_gnss + 0.195) < 1e-6);
            for i = 1:length(idxA)
                a = idxA(i);
                % 在a之后找第一个diff不等于period的点
                b = a + find(abs(diff_gnss(a+1:end) - period) > 1e-6, 1, 'first');
                if isempty(b)
                    continue;
                end
                % 检查b的diff是否为0.205，且b-a==200
                if abs(diff_gnss(b) - 0.205) < 1e-6 && (b - a == 200)
                    obj.dataStruct.Time.GNSSSeconds(a+1:b,:) = obj.dataStruct.Time.GNSSSeconds(a+1:b,:) + 0.2;
                    fprintf('已修正区间：第%d到第%d帧的GNSS毫秒(+0.200s)\n', a+1, b);
                end
            end
        end
    end

    methods (Static)
        function vec = compensateJumps(vec, jumpIdx)
            % 静态方法：对突变点进行补偿
            for i = 1:length(jumpIdx)
                idx = jumpIdx(i);
                % 找左侧最近的非突变点
                left = idx-1;
                while left >= 1 && ismember(left, jumpIdx)
                    left = left-1;
                end
                % 找右侧最近的非突变点
                right = idx+1;
                while right <= length(vec) && ismember(right, jumpIdx)
                    right = right+1;
                end
                if left >= 1 && right <= length(vec)
                    vec(idx) = mean([vec(left), vec(right)]);
                elseif left >= 1
                    vec(idx) = vec(left);
                elseif right <= length(vec)
                    vec(idx) = vec(right);
                end
            end
        end
    end
end
