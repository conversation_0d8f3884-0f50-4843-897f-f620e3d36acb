function mergeCSVbyCreationTime(inputFolder, outputFile, varargin)
% MERGECSVBYCREATIONTIME 按照创建时间顺序合并多个CSV文件
%   输入参数:
%       inputFolder - 包含CSV文件的文件夹路径
%       outputFile - 合并后的输出文件路径
%       varargin - 可选参数，传递给readtable的额外选项（如'Delimiter', ','）
%
%   示例用法:
%       mergeCSVbyCreationTime('C:/data/csv_files', 'C:/output/merged.csv')
%       mergeCSVbyCreationTime('C:/data/csv_files', 'C:/output/merged.csv', 'Delimiter', ';')

% 获取文件夹中所有CSV文件
fileList = dir(fullfile(inputFolder, '*.csv'));

% 检查是否找到文件
if isempty(fileList)
    error('未找到任何CSV文件');
end

% 提取文件信息和创建时间
fileInfo = struct('name', {}, 'date', {}, 'datenum', {});
for i = 1:length(fileList)
    fileInfo(i).name = fullfile(inputFolder, fileList(i).name);
    fileInfo(i).date = fileList(i).date;
    fileInfo(i).datenum = fileList(i).datenum;
end

% 按照创建时间排序
[~, idx] = sort([fileInfo.datenum]);
sortedFiles = fileInfo(idx);

% 初始化标志和参考列数
headerWritten = false;
refWidth = [];

% 创建或清空输出文件
fid = fopen(outputFile, 'w');
if fid == -1
    error('无法创建输出文件: %s', outputFile);
end
fclose(fid);

% 逐个读取并追加文件内容
for i = 1:length(sortedFiles)
    % 读取当前CSV文件
    try
        currentData = readtable(sortedFiles(i).name, varargin{:});
    catch ME
        warning('无法读取文件: %s\n错误信息: %s', sortedFiles(i).name, ME.message);
        continue;
    end
    
    % 如果是第一个有效文件，写入表头并记录列数
    if ~headerWritten
        writetable(currentData, outputFile, 'WriteMode', 'overwrite');
        headerWritten = true;
        refWidth = width(currentData);
    else
        % 检查列数是否匹配
        if width(currentData) == refWidth
            writetable(currentData, outputFile, 'WriteMode', 'append', ...
                     'WriteVariableNames', false);
        else
            warning('文件 %s 的列数(%d)与参考列数(%d)不匹配，跳过此文件', ...
                   sortedFiles(i).name, width(currentData), refWidth);
        end
    end
end

fprintf('成功合并 %d/%d 个CSV文件到: %s\n', sum(headerWritten), length(sortedFiles), outputFile);
end