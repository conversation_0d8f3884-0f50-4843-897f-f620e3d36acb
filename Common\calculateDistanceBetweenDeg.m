function distance = calculateDistanceBetweenDeg(deg1, deg2)
    % 函数：计算两个DMS格式坐标之间的直线距离
    % 输入：
    %   deg1: 第一个坐标的DMS格式，例如 [经度度, 纬度度]
    %   deg2: 第二个坐标的DMS格式，例如 [经度度, 纬度度]
    % 输出：
    %   distance: 两点之间的直线距离（单位：米）

    % 将DMS格式转换为十进制度
    lon1 = deg1(1);
    lat1 = deg1(2);
    lon2 = deg2(1);
    lat2 = deg2(2);

    % 将十进制度转换为弧度
    lat1 = deg2rad(lat1);
    lon1 = deg2rad(lon1);
    lat2 = deg2rad(lat2);
    lon2 = deg2rad(lon2);

    % 地球半径（单位：米）
    R = 6371000;

    % Haversine公式计算距离
    dlat = lat2 - lat1;
    dlon = lon2 - lon1;
    a = sin(dlat/2)^2 + cos(lat1) * cos(lat2) * sin(dlon/2)^2;
    c = 2 * atan2(sqrt(a), sqrt(1-a));
    distance = R * c;
end