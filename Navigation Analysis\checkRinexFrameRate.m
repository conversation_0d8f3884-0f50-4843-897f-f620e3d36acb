function [isValid, details] = checkRinexFrameRate(filePath, expectedRate)
% checkRinexFrameRate 检查RINEX观测文件(.25O)中的时间戳是否按照预期频率更新
%
% 输入:
%   filePath - RINEX观测文件(.25O)的路径
%   expectedRate - 预期的数据更新频率，单位Hz（默认为1Hz）
%
% 输出:
%   isValid - 布尔值，表示数据是否有效（时间间隔是否一致）
%   details - 结构体，包含检查的详细信息
%     .expectedInterval - 预期的时间间隔（秒）
%     .actualIntervals - 实际的时间间隔数组
%     .timestamps - 提取的时间戳数组
%     .anomalies - 异常点的索引和值
%     .statistics - 统计信息（最小值、最大值、平均值、标准差）
%     .droppedFrames - 丢帧信息
%       .count - 总丢帧数
%       .locations - 丢帧位置
%       .framesPerDrop - 每个位置丢失的帧数
%
% 示例:
%   [isValid, details] = checkRinexFrameRate('data.25O');
%   [isValid, details] = checkRinexFrameRate('data.25O', 1); % 1Hz
%   plotRinexFrameRate(details); % 可视化结果

% 处理可选参数
if nargin < 2
    expectedRate = 1; % 默认预期频率为1Hz
end

% 预期的时间间隔（秒）
expectedInterval = 1 / expectedRate;

% 检查文件是否存在
if ~exist(filePath, 'file')
    error('文件不存在: %s', filePath);
end

% 读取文件
fid = fopen(filePath, 'r');
if fid == -1
    error('无法打开文件: %s', filePath);
end

% 初始化变量
timestamps = [];
lineCount = 0;

% 逐行读取文件
while ~feof(fid)
    line = fgetl(fid);
    lineCount = lineCount + 1;
    
    % 检查是否是时间戳行（以'>'开头）
    if ~isempty(line) && line(1) == '>'
        % 解析时间戳
        try
            % 格式: > YYYY MM DD HH MM SS.SSSSSSS
            parts = sscanf(line, '> %d %d %d %d %d %f');
            if length(parts) >= 6
                year = parts(1);
                month = parts(2);
                day = parts(3);
                hour = parts(4);
                minute = parts(5);
                second = parts(6);
                
                % 转换为datetime对象
                dt = datetime(year, month, day, hour, minute, second);
                
                % 转换为秒数（从文件开始的相对时间）
                if isempty(timestamps)
                    baseTime = dt;
                    timestamps(end+1) = 0;
                else
                    timestamps(end+1) = seconds(dt - baseTime);
                end
            end
        catch
            warning('无法解析第%d行的时间戳: %s', lineCount, line);
        end
    end
end

% 关闭文件
fclose(fid);

% 检查是否找到时间戳
if isempty(timestamps)
    error('未找到任何时间戳');
end

% 计算时间间隔
timeIntervals = diff(timestamps);

% 找出异常点（与预期间隔相差超过5%的点）
tolerance = 0.05 * expectedInterval; % 5%的容差
anomalyIndices = find(abs(timeIntervals - expectedInterval) > tolerance);
anomalyValues = timeIntervals(anomalyIndices);

% 计算统计信息
stats.min = min(timeIntervals);
stats.max = max(timeIntervals);
stats.mean = mean(timeIntervals);
stats.std = std(timeIntervals);
stats.median = median(timeIntervals);

% 判断数据是否有效（没有异常点）
isValid = isempty(anomalyIndices);

% 计算丢帧信息
droppedFrames = struct('count', 0, 'locations', [], 'framesPerDrop', []);

if ~isempty(anomalyIndices)
    % 对于每个异常点，计算丢失的帧数
    droppedFramesLocations = anomalyIndices; % 丢帧位置（索引）
    droppedFramesCount = zeros(size(droppedFramesLocations)); % 每个位置丢失的帧数
    
    for i = 1:length(anomalyIndices)
        idx = anomalyIndices(i);
        actualInterval = timeIntervals(idx);
        
        % 计算丢失的帧数（向下取整，因为可能有小的时间误差）
        lostFrames = round(actualInterval / expectedInterval) - 1;
        
        % 确保丢失的帧数至少为1
        if lostFrames < 1
            % 如果间隔明显大于预期但计算出的丢帧数小于1，则认为至少丢失了1帧
            if actualInterval > (1.5 * expectedInterval)
                lostFrames = 1;
            else
                lostFrames = 0; % 可能是其他类型的异常，不是丢帧
            end
        end
        
        droppedFramesCount(i) = lostFrames;
    end
    
    % 过滤掉不是由丢帧引起的异常（丢帧数为0的点）
    validDrops = droppedFramesCount > 0;
    droppedFramesLocations = droppedFramesLocations(validDrops);
    droppedFramesCount = droppedFramesCount(validDrops);
    
    % 计算总丢帧数
    totalDroppedFrames = sum(droppedFramesCount);
    
    % 保存丢帧信息
    droppedFrames.count = totalDroppedFrames;
    droppedFrames.locations = droppedFramesLocations;
    droppedFrames.framesPerDrop = droppedFramesCount;
end

% 构建详细信息结构体
details.expectedInterval = expectedInterval;
details.actualIntervals = timeIntervals;
details.timestamps = timestamps;
details.anomalies.indices = anomalyIndices;
details.anomalies.values = anomalyValues;
details.statistics = stats;
details.droppedFrames = droppedFrames;

% 输出检查结果
if isValid
    fprintf('RINEX文件时间戳检查通过：时间间隔一致\n');
    fprintf('预期时间间隔: %.3f 秒 (%.1f Hz)\n', expectedInterval, expectedRate);
    fprintf('未检测到丢帧\n');
else
    fprintf('RINEX文件时间戳检查失败：发现异常时间间隔\n');
    fprintf('预期时间间隔: %.3f 秒 (%.1f Hz)\n', expectedInterval, expectedRate);
    fprintf('发现 %d 个异常点\n', length(anomalyIndices));
    fprintf('时间间隔范围: %.3f 到 %.3f 秒\n', stats.min, stats.max);
    
    % 显示丢帧信息
    if droppedFrames.count > 0
        fprintf('检测到总丢帧数: %d 帧\n', droppedFrames.count);
        fprintf('丢帧发生次数: %d 次\n', length(droppedFrames.locations));
        
        % 如果丢帧次数不多，显示详细信息
        if length(droppedFrames.locations) <= 10
            fprintf('丢帧详情:\n');
            for i = 1:length(droppedFrames.locations)
                idx = droppedFrames.locations(i);
                lostFrames = droppedFrames.framesPerDrop(i);
                fprintf('  索引 %d: 丢失 %d 帧, 时间从 %.3f 到 %.3f, 间隔: %.3f 秒\n', ...
                    idx, lostFrames, timestamps(idx), timestamps(idx+1), timeIntervals(idx));
            end
        end
    else
        fprintf('未检测到丢帧，异常可能由其他原因引起\n');
    end
end

end
