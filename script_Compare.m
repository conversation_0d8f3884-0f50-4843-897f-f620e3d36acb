close all

% experiment_str = "1-3-aca";
% experiment_str = "4-rtk-ppk-5hz";
% experiment_str = "2-2-a";
experiment_str = "4-rkt-ppk";

methods_str = {"OnlyINS","PPP","PPK","LC","TC","TC2"};
method_selected = [1,5];

IEprocessObj = struct( methods_str{1},[], ...
                       methods_str{2},[], ...
                       methods_str{3},[], ...
                       methods_str{4},[], ...
                       methods_str{5},[], ...
                       methods_str{6},[]);

for kk = method_selected
    ggaFilename = strcat(experiment_str, '_', methods_str{kk}, '.txt');
    ggaobj = GPGGAParser4TLH(fullfile(IEprojectFolderpath, ggaFilename));
    IEprocessObj.(methods_str{kk}) = ggaobj.toComNavResult;
end

for ii = 0:length(methods_str)
    if ii == 0
        comnavobj = INSobj; 
        idx = find(comnavobj.attitude(:,3) < -180);
        comnavobj.attitude(idx,3) = comnavobj.attitude(idx,3) + 360;
        idx = find(comnavobj.attitude(:,3) > 180);
        comnavobj.attitude(idx,3) = comnavobj.attitude(idx,3) - 360;
        displayname = "组合导航";
    else
        if isempty(IEprocessObj.(methods_str{ii}))
            continue;
        else
            comnavobj = IEprocessObj.(methods_str{ii});
            displayname = methods_str{ii};
        end
    end
    figure(1)
    compareNaviResult(comnavobj, 'position', displayname);
    figure(2)
    compareNaviResult(comnavobj, 'attitude', displayname);
    figure(3)
    compareNaviResult(comnavobj, 'velocity', displayname);
end

function compareNaviResult(comnavobj, type, displayname)
    subplot(3,1,1)
    hold on
    plot(comnavobj.gnssTow,comnavobj.(type)(:,1), "DisplayName", displayname)
    legend 
    subplot(3,1,2)
    hold on
    plot(comnavobj.gnssTow,comnavobj.(type)(:,2), "DisplayName", displayname)
    legend 
    subplot(3,1,3)
    hold on
    plot(comnavobj.gnssTow,comnavobj.(type)(:,3), "DisplayName", displayname)
    legend 
end