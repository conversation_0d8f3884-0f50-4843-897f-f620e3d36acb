classdef HMRFileGenerator
    % HMRFileGenerator 用于将原始数据（.csv文件）按照特定协议封装为HMR文件
    %
    % 详细描述类的功能和用途。
    %
    % 属性：
    %   - Property1: 描述属性1的作用
    %   - Property2: 描述属性2的作用
    %   ...
    %
    % 方法：
    %   - methodName1: 描述方法1的作用
    %   - methodName2: 描述方法2的作用
    %   ...
    %
    % 示例：
    %   obj = MyClass(input1, input2);
    %   result = obj.methodName1();
    %
    % 注意事项：
    %   - MD01第二版结构安装参数(m)
    %   -   GNSS从天线的杆臂参数为：右前上 0.04054, 0.74040, -0.05050
    %   -   基线长为1.4888
    %
    % 版本：
    %   - 1.0.0 (2025-03-06): 初始版本
    %
    % 作者：
    %   - 利俊纬 <<EMAIL>>
    %
    % 版权：
    %   - Copyright (c) 2025 你的名字或公司名称
    %
    % 许可证：
    %   - MIT License 或其他许可证信息

    properties
        csvFilePath % CSV文件路径
        hmrFilePath % HMR文件路径
        frameHeader % 帧头结构（256字节）
        rawDataFrames % 字符串形式的数据
        dataFrames  % 数据帧结构（多个帧）
        numFrames   % 帧的数量

        ucType      % 1 = 外部源, 2 = 双天线源
        BaselineLength % 基线长度

        szImuName       = 'INS370-25J'
        szProgramName   = 'MD01'
    end
    
    methods
        function obj = HMRFileGenerator(inputSource, hmrFilePath)
            % 构造函数
            % 输入:
            %   inputSource - 可以是CSV文件路径，或ComNavResult对象
            %   hmrFilePath - HMR文件路径
            if isprop(obj, 'szImuName') && strcmp(obj.szImuName, 'INS370-25J')
                obj.ucType = 2; % 双天线源
                obj.BaselineLength = 1.4888; % 基线长度
            else
                obj.ucType = 1; % 外部源
                obj.BaselineLength = 0; % 外部源不使用基线长度
            end

            if ischar(inputSource) || isstring(inputSource)
                csvFilePath = inputSource;
                obj.csvFilePath = csvFilePath;
                obj.hmrFilePath = hmrFilePath;
                obj.numFrames = 0;
                obj = obj.readCSV();
                disp(['读取CSV文件完成，共读取到 ', num2str(obj.numFrames), ' 帧数据']);

            elseif isa(inputSource, 'ComNavResult')
                navObj = inputSource;
                obj.csvFilePath = '';
                obj.hmrFilePath = hmrFilePath;
                % 只保留有GNSS数据的帧
                if isempty(navObj.gnssTow) || isempty(navObj.gnssWeek) || isempty(navObj.attitude)
                    error('ComNavResult对象缺少gnssTow、gnssWeek或attitude数据');
                end
                % dGpsTime: gnssTow, sGpsWeek: gnssWeek, dHeading: attitude(:,3), dPitch: attitude(:,2)
                obj.rawDataFrames = [navObj.gnssTow(:), navObj.gnssWeek(:), navObj.attitude(:,3)-90, navObj.attitude(:,2)];
                obj.numFrames = size(obj.rawDataFrames, 1);
                disp(['从ComNavResult对象提取数据，共 ', num2str(obj.numFrames), ' 帧']);
            else
                error('输入类型不支持，必须为CSV文件路径或ComNavResult对象');
            end
        end
        
        function obj = readCSV(obj)
            % 读取CSV文件
            % 这里假设CSV文件的每一行代表一个帧的数据
            % 使用readmatrix读取数据
            data = readmatrix(obj.csvFilePath);
            
            % 将数据存储到rawDataFrames属性中
                % obj.rawDataFrames.dGpsTime = data(:,20);
                % obj.rawDataFrames.sGpsWeek = data(:,21);
                % obj.rawDataFrames.dHeading = data(:,3);
            obj.rawDataFrames = data(:,[20,21,3]);

            obj.numFrames = size(data, 1); % 获取帧的数量
            disp(['读取CSV文件完成，共读取到 ', num2str(obj.numFrames), ' 帧数据']);
        end
        
        function obj = generateFrameHeader(obj)
            % 生成帧头
            
            dBoreSightRotationZ = 0.0; % 假设为0.0，实际值需要根据具体情况设置
            dBoreSightRotationZStdDev = 0.0; % 未知，设为0.0

            % 初始化帧头为256字节的零数组
            obj.frameHeader = zeros(1, 256, 'uint8');
            
            % 1. szTitleStr (12字节): "$IMUHEADING\0"
            titleStr = '$IMUHEADING';
            obj.frameHeader(1:12) = [uint8(titleStr), 0]; % NULL终止
            
            % 2. ucType (1字节): 1或2
            obj.frameHeader(13) = uint8(obj.ucType);
            
            % 3. dBoreSightRotationZ (8字节): double类型
            obj.frameHeader(14:21) = typecast(dBoreSightRotationZ, 'uint8');
            
            % 4. dBoreSightRotationZStdDev (8字节): double类型
            obj.frameHeader(22:29) = typecast(dBoreSightRotationZStdDev, 'uint8');
            
            % 5. Extra (227字节): 保留字段，填充0
            obj.frameHeader(30:256) = 0; % 填充0
            disp('生成帧头完成');
        end
        
        function obj = generateDataFrames(obj)
            % 生成所有数据帧
            disp('生成数据帧...');
            for i = 1:obj.numFrames
                frameData = obj.rawDataFrames(i, :); % 获取第i帧的数据
                % 根据协议处理帧数据
                processedFrame = obj.processFrameData(frameData); % 处理帧数据
                obj.dataFrames(i, :) = processedFrame; % 更新数据帧

                if mod(i, 1e4) == 0
                    % 计算并显示写入进度
                    progress = i / obj.numFrames * 100; % 计算进度百分比
                    fprintf('写入进度: %.2f%%\n', progress); % 显示进度
                end
            end
            disp('数据帧生成完成');
        end
        
        function processedFrame = processFrameData(obj, frameData)
            % 处理单个帧的数据
            % 输入:
            %   frameData - 原始帧数据（从CSV文件中读取的一行）
            % 输出:
            %   processedFrame - 处理后的帧数据（符合协议要求）
            
            % 1. dGpsTime (8字节): double类型
            dGpsTime = frameData(1); % 第1列是dGpsTime
            dGpsTimeBytes = typecast(dGpsTime, 'uint8');
            
            % 2. sGpsWeek (2字节): int16_t类型
            sGpsWeek = int16(frameData(2)); % 第2列是sGpsWeek
            sGpsWeekBytes = typecast(sGpsWeek, 'uint8');
            
            % 3. dHeading (8字节): double类型
            dHeading = frameData(3); % 第3列是dHeading
            dHeadingBytes = typecast(dHeading, 'uint8');
            
            % 4. fHeadingStdDev (4字节): float类型
            % 由于协议未提供，目前采用指定模式
            % HeadingStdDev = 0.1; % RTK的双天线姿态精度
            HeadingStdDev = 3.0; % PPP的双天线姿态精度
            fHeadingStdDev = single(HeadingStdDev); 
            fHeadingStdDevBytes = typecast(fHeadingStdDev, 'uint8');
            
            % 5. fBaselineLength (4字节): float类型（仅当ucType == 2时使用）
            if obj.ucType == 2
                fBaselineLength = single(obj.BaselineLength); 
            else
                fBaselineLength = single(0); % 如果不使用，填充0
            end
            fBaselineLengthBytes = typecast(fBaselineLength, 'uint8');
            
            % 6. fPitch (4字节): float类型（仅当ucType == 2时使用）
            dPitch = frameData(4);
            if obj.ucType == 2
                fPitch = single(dPitch); % 假设CSV文件的第6列是fPitch
            else
                fPitch = single(0); % 如果不使用，填充0
            end
            fPitchBytes = typecast(fPitch, 'uint8');
            
            % 7. fPitchStdDev (4字节): float类型（仅当ucType == 2时使用）
            % 由于协议未提供，目前采用指定模式
            % PitchStdDev = 0.1; % RTK的双天线姿态精度
            PitchStdDev = 3.0; % PPP的双天线姿态精度
            if obj.ucType == 2
                fPitchStdDev = single(PitchStdDev); % 假设CSV文件的第7列是fPitchStdDev
            else
                fPitchStdDev = single(0); % 如果不使用，填充0
            end
            fPitchStdDevBytes = typecast(fPitchStdDev, 'uint8');
            
            % 将处理后的数据帧拼接为一个字节数组
            processedFrame = [dGpsTimeBytes, sGpsWeekBytes, dHeadingBytes, ...
                              fHeadingStdDevBytes, fBaselineLengthBytes, ...
                              fPitchBytes, fPitchStdDevBytes];
        end
        
        function obj = writeHMRFile(obj)
            % 将帧头和数据帧写入HMR文件
            % 打开文件
            fileID = fopen(obj.hmrFilePath, 'wb'); % 以二进制写入模式打开文件
            if fileID == -1
                error('无法打开HMR文件');
            end
            
            % 写入帧头
            fwrite(fileID, obj.frameHeader, 'uint8');

            % 写入所有数据帧（每个帧前添加帧头）
            for i = 1:obj.numFrames
                % 写入数据帧
                frameData = obj.dataFrames(i, :); % 获取第i帧的数据
                fwrite(fileID, frameData, 'uint8'); % 写入帧数据
            end
            
            % 关闭文件
            fclose(fileID);
            disp(['HMR文件已写入：', obj.hmrFilePath]);
        end
        
        function generateHMR(obj)
            % 生成HMR文件的完整流程
            obj = obj.generateFrameHeader();
            obj = obj.generateDataFrames();
            obj.writeHMRFile();
            disp('HMR文件生成完成！');
        end
    end
end