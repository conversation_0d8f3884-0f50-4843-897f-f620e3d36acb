
bddb1bobj = BDDB1BParser(csvfilepath);
INSobj = bddb1bobj.toComNavResult;

%% 绘制经纬高曲线
subplot(3,1,1)
hold on
plot(INSobj.gnssTow,INSobj.position(:,1))
subplot(3,1,2)
hold on
plot(INSobj.gnssTow,INSobj.position(:,2))
subplot(3,1,3)
hold on
plot(INSobj.gnssTow,INSobj.position(:,3))

%% 保存组合导航对象
NavobjSavePath = fullfile(projectFolderpath,"Analysis_data");
if ~exist("NavobjSavePath","dir")
    mkdir(NavobjSavePath);
end
NavobjSaveFilepath = fullfile(NavobjSavePath, "INSobj");
save(NavobjSaveFilepath,"INSobj");
disp("组合导航文件已经保存到本地。")